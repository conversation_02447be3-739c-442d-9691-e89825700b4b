#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 257949696 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3771), pid=29648, tid=11288
#
# JRE version:  (17.0.7+10) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.7+10-b829.16, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://codeup.aliyun.com': 

Host: AMD Ryzen 7 4800U with Radeon Graphics         , 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3235)
Time: Tue Mar 19 14:43:21 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.3235) elapsed time: 0.026579 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001159b3bda00):  JavaThread "Unknown thread" [_thread_in_vm, id=11288, stack(0x00000013bd500000,0x00000013bd600000)]

Stack: [0x00000013bd500000,0x00000013bd600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x683cca]
V  [jvm.dll+0x8424b4]
V  [jvm.dll+0x843cae]
V  [jvm.dll+0x844313]
V  [jvm.dll+0x24ad2f]
V  [jvm.dll+0x680b99]
V  [jvm.dll+0x67526a]
V  [jvm.dll+0x30b3cb]
V  [jvm.dll+0x312876]
V  [jvm.dll+0x36221e]
V  [jvm.dll+0x36244f]
V  [jvm.dll+0x2e14a8]
V  [jvm.dll+0x2e2414]
V  [jvm.dll+0x814441]
V  [jvm.dll+0x36ffe1]
V  [jvm.dll+0x7f3a1c]
V  [jvm.dll+0x3f305f]
V  [jvm.dll+0x3f4b91]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff9547bb098, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001159b428c40 GCTaskThread "GC Thread#0" [stack: 0x00000013bd600000,0x00000013bd700000] [id=18572]
  0x000001159b439150 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000013bd700000,0x00000013bd800000] [id=28944]
  0x000001159b43c210 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000013bd800000,0x00000013bd900000] [id=25884]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff953f72087]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001159b3ba070] Heap_lock - owner thread: 0x000001159b3bda00

Heap address: 0x000000070a200000, size: 3934 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x000000070a200000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff95435df59]

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.017 Loaded shared library D:\JetBrains\WebStorm 2023.1.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6e8100000 - 0x00007ff6e810a000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\java.exe
0x00007ff9b7a50000 - 0x00007ff9b7c66000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff9b6750000 - 0x00007ff9b6814000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff9b50e0000 - 0x00007ff9b5486000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff9b5560000 - 0x00007ff9b5671000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff987000000 - 0x00007ff987017000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\jli.dll
0x00007ff9af960000 - 0x00007ff9af97b000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ff9b7860000 - 0x00007ff9b7a0d000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9b5680000 - 0x00007ff9b56a6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff9b69e0000 - 0x00007ff9b6a09000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff9967b0000 - 0x00007ff996a43000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98\COMCTL32.dll
0x00007ff9b4f00000 - 0x00007ff9b5018000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9b7610000 - 0x00007ff9b76b7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff9b5490000 - 0x00007ff9b552a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff9b7570000 - 0x00007ff9b75a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff9af9f0000 - 0x00007ff9af9fc000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff99e000000 - 0x00007ff99e08d000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\msvcp140.dll
0x00007ff953c80000 - 0x00007ff9548ee000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\server\jvm.dll
0x00007ff9b77a0000 - 0x00007ff9b7852000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff9b7350000 - 0x00007ff9b73f8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff9b5530000 - 0x00007ff9b5558000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff9b68a0000 - 0x00007ff9b69b5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff99d920000 - 0x00007ff99d929000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff9aab40000 - 0x00007ff9aab4a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff9af0e0000 - 0x00007ff9af114000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff9b66d0000 - 0x00007ff9b6741000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff9b3dd0000 - 0x00007ff9b3de8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff9982f0000 - 0x00007ff9982fa000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\jimage.dll
0x00007ff9b2420000 - 0x00007ff9b2653000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff9b5c80000 - 0x00007ff9b6008000 	C:\WINDOWS\System32\combase.dll
0x00007ff9b76c0000 - 0x00007ff9b7797000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9925a0000 - 0x00007ff9925d2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff9b5720000 - 0x00007ff9b579a000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff986f30000 - 0x00007ff986f55000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\JetBrains\WebStorm 2023.1.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98;D:\JetBrains\WebStorm 2023.1.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://codeup.aliyun.com': 
java_class_path (initial): D:/JetBrains/WebStorm 2023.1.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/JetBrains/WebStorm 2023.1.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4125097984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4125097984                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:/Git/mingw64/libexec/git-core;D:/Git/mingw64/libexec/git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Git\cmd;C:\Program Files (x86)\HIK-share\;D:\Soft\;D:\Soft\xftp\;D:\Tencent\΢��web�����߹���\dll;C:\Program Files\TortoiseGit\bin;C:\Program Files (x86)\Yarn\bin\;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\nodejs;C:\Users\<USER>\AppData\Local\Yarn\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=xushe
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:

[error occurred during error reporting (JNI global references), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff953d37fc0]


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3235)
OS uptime: 0 days 6:05 hours

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 15734M (3983M free)
TotalPageFile size 28534M (AvailPageFile size 132M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 70M, peak: 316M

vm_info: OpenJDK 64-Bit Server VM (17.0.7+10-b829.16) for windows-amd64 JRE (17.0.7+10-b829.16), built on 2023-06-02 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
