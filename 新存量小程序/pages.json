{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
        	"path" : "pages/index/index",
        	"style" : 
        	{
        		"navigationBarTitleText" : "悦乐趣权益包"
        	}
        },
		{
			"path" : "pages/index/qualification/qualification",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包悦乐趣权益包"
			}
		},
		{
			"path" : "pages/index/order/order",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/indexList/indexList",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/orderList/orderList",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/success/success",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
        {
        	"path" : "pages/user/user",
        	"style" : 
        	{
        		"navigationBarTitleText" : "悦乐趣权益包"
        	}
        },
		{
			"path" : "pages/HBLT-LL-dyks20/HBLT-LL-dyks20",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/HB-cailing/HB-cailing",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/HBLT-LL-cyyx20/HBLT-LL-cyyx20",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/LT-yhzx/LT-yhzx",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/LT-yhzx-new/LT-yhzx-new",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/CL-mode/CL-mode",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/WZRY-mode/WZRY-mode",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/YD-xjyd/YD-xjyd",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/success-common/success-common",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/index/webview/webview",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/success/success",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/WZRY-CJ/wzry-cj",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/success-page/success-page",
			"style" : 
			{
				"navigationBarTitleText" : "悦乐趣权益包"
			}
		},
		{
			"path" : "pages/index/webview/webview",
			"style" :
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		}
    ],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "弘瑞会员权益包",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "##F8F8F8",
		"selectedColor": "#d83337",
		"backgroundColor": "#ffffff",
		"fontSize": "10px",
		"spacing": "5px",
		"list": [{
			"pagePath": "pages/index/index",
			"iconPath": "static/img/icon/b1.png",
			"selectedIconPath": "static/img/icon/b2.png",
			"text": "首页"
		},{
			"pagePath": "pages/orderList/orderList",
			"iconPath": "static/img/icon/b3.png",
			"selectedIconPath": "static/img/icon/b4.png",
			"text": "订单"
		}, {
			"pagePath": "pages/user/user",
			"iconPath": "static/img/icon/b5.png",
			"selectedIconPath": "static/img/icon/b6.png",
			"text": "我的"
		}]
	},
	"uniIdRouter": {}
}
