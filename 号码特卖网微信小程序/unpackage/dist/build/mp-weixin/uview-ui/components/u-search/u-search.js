(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uview-ui/components/u-search/u-search"],{5225:function(t,e,n){},"5df3":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"u-search",props:{shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:function(){return{}}},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},disabled:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},borderColor:{type:String,default:"none"},value:{type:String,default:""},height:{type:[Number,String],default:64},inputStyle:{type:Object,default:function(){return{}}},maxlength:{type:[Number,String],default:"-1"},searchIconColor:{type:String,default:""},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},margin:{type:String,default:"0"},searchIcon:{type:String,default:"search"}},data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!(this.animation||!this.showAction)},borderStyle:function(){return this.borderColor?"1px solid ".concat(this.borderColor):"none"}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(e){this.$emit("search",e.detail.value);try{t.hideKeyboard()}catch(e){}},custom:function(){this.$emit("custom",this.keyword);try{t.hideKeyboard()}catch(e){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")}}};e.default=n}).call(this,n("543d")["default"])},b43b:function(t,e,n){"use strict";n.r(e);var i=n("ca73"),o=n("b57b");for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(u);n("b570");var r=n("f0c5"),a=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"1239be8d",null,!1,i["a"],void 0);e["default"]=a.exports},b570:function(t,e,n){"use strict";var i=n("5225"),o=n.n(i);o.a},b57b:function(t,e,n){"use strict";n.r(e);var i=n("5df3"),o=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=o.a},ca73:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return i}));var i={uIcon:function(){return n.e("uview-ui/components/u-icon/u-icon").then(n.bind(null,"897d"))}},o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.__get_style([{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor},t.inputStyle])),i=t.__get_style([t.actionStyle]);t.$mp.data=Object.assign({},{$root:{s0:n,s1:i}})},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uview-ui/components/u-search/u-search-create-component',
    {
        'uview-ui/components/u-search/u-search-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("b43b"))
        })
    },
    [['uview-ui/components/u-search/u-search-create-component']]
]);
