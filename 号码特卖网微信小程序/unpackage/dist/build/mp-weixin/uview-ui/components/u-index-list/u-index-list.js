(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uview-ui/components/u-index-list/u-index-list"],{"13ae":function(t,e,n){},"1b33":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},o=[]},"36c7":function(t,e,n){"use strict";var i=n("13ae"),o=n.n(i);o.a},6367:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"u-index-list",props:{sticky:{type:Boolean,default:!0},zIndex:{type:[Number,String],default:""},scrollTop:{type:[Number,String],default:0},offsetTop:{type:[Number,String],default:0},indexList:{type:Array,default:function(){return function(){for(var t=[],e="A".charCodeAt(0),n=0;n<26;n++)t.push(String.fromCharCode(e+n));return t}()}},activeColor:{type:String,default:"#2979ff"}},created:function(){this.stickyOffsetTop=this.offsetTop?t.upx2px(this.offsetTop):0,this.children=[]},data:function(){return{activeAnchorIndex:0,showSidebar:!0,touchmove:!1,touchmoveIndex:0}},watch:{scrollTop:function(){this.updateData()}},computed:{alertZIndex:function(){return this.$u.zIndex.toast}},methods:{updateData:function(){var t=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){t.showSidebar=!!t.children.length,t.setRect().then((function(){t.onScroll()}))}),0)},setRect:function(){return Promise.all([this.setAnchorsRect(),this.setListRect(),this.setSiderbarRect()])},setAnchorsRect:function(){return Promise.all(this.children.map((function(t,e){return t.$uGetRect(".u-index-anchor-wrapper").then((function(e){Object.assign(t,{height:e.height,top:e.top})}))})))},setListRect:function(){var t=this;return this.$uGetRect(".u-index-bar").then((function(e){Object.assign(t,{height:e.height,top:e.top+t.scrollTop})}))},setSiderbarRect:function(){var t=this;return this.$uGetRect(".u-index-bar__sidebar").then((function(e){t.sidebar={height:e.height,top:e.top}}))},getActiveAnchorIndex:function(){for(var t=this.children,e=this.sticky,n=this.children.length-1;n>=0;n--){var i=n>0?t[n-1].height:0,o=e?i:0;if(o>=t[n].top)return n}return-1},onScroll:function(){var t=this,e=this.children,n=void 0===e?[]:e;if(n.length){var i=this.sticky,o=this.stickyOffsetTop,r=this.zIndex,c=(this.scrollTop,this.activeColor),s=this.getActiveAnchorIndex();if(this.activeAnchorIndex=s,i){var h=!1;-1!==s&&(h=n[s].top<=0),n.forEach((function(e,i){if(i===s){var a="",u={color:"".concat(c)};h&&(a={height:"".concat(n[i].height,"px")},u={position:"fixed",top:"".concat(o,"px"),zIndex:"".concat(r||t.$u.zIndex.indexListSticky),color:"".concat(c)}),e.active=s,e.wrapperStyle=a,e.anchorStyle=u}else if(i===s-1){var l=n[i],f=l.top,d=i===n.length-1?t.top:n[i+1].top,p=d-f,v=p-l.height,x={position:"relative",transform:"translate3d(0, ".concat(v,"px, 0)"),zIndex:"".concat(r||t.$u.zIndex.indexListSticky),color:"".concat(c)};e.active=s,e.anchorStyle=x}else e.active=!1,e.anchorStyle="",e.wrapperStyle=""}))}}},onTouchMove:function(t){this.touchmove=!0;var e,n=this.children.length,i=t.touches[0],o=this.sidebar.height/n;e=i.clientY;var r=Math.floor((e-this.sidebar.top)/o);r<0?r=0:r>n-1&&(r=n-1),this.touchmoveIndex=r,this.scrollToAnchor(r)},onTouchStop:function(){this.touchmove=!1,this.scrollToAnchorIndex=null},scrollToAnchor:function(e){var n=this;if(this.scrollToAnchorIndex!==e){this.scrollToAnchorIndex=e;var i=this.children.find((function(t){return t.index===n.indexList[e]}));i&&(this.$emit("select",i.index),t.pageScrollTo({duration:0,scrollTop:i.top+this.scrollTop}))}}}};e.default=n}).call(this,n("543d")["default"])},7022:function(t,e,n){"use strict";n.r(e);var i=n("6367"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},"8d43":function(t,e,n){"use strict";n.r(e);var i=n("1b33"),o=n("7022");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("36c7");var c=n("f0c5"),s=Object(c["a"])(o["default"],i["b"],i["c"],!1,null,"2a142fb6",null,!1,i["a"],void 0);e["default"]=s.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uview-ui/components/u-index-list/u-index-list-create-component',
    {
        'uview-ui/components/u-index-list/u-index-list-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("8d43"))
        })
    },
    [['uview-ui/components/u-index-list/u-index-list-create-component']]
]);
