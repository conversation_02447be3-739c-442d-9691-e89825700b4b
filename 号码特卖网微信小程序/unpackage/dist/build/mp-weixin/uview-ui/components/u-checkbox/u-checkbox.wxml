<view class="u-checkbox data-v-2f972247" style="{{$root.s0}}"><view data-event-opts="{{[['tap',[['toggle',['$event']]]]]}}" class="{{['u-checkbox__icon-wrap','data-v-2f972247',iconClass]}}" style="{{$root.s1}}" bindtap="__e"><u-icon class="u-checkbox__icon-wrap__icon data-v-2f972247" vue-id="40f28ce6-1" name="checkbox-mark" size="{{checkboxIconSize}}" color="{{iconColor}}" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['onClickLabel',['$event']]]]]}}" class="u-checkbox__label data-v-2f972247" style="{{'font-size:'+($root.g0)+';'}}" bindtap="__e"><slot></slot></view></view>