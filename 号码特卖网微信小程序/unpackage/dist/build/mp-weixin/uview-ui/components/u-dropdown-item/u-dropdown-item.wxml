<block wx:if="{{active}}"><view data-event-opts="{{[['touchmove',[['e0',['$event']]]],['tap',[['e1',['$event']]]]]}}" class="u-dropdown-item data-v-4218a3ca" catchtouchmove="__e" catchtap="__e"><block wx:if="{{!$slots.default&&!$slots.$default}}"><block class="data-v-4218a3ca"><scroll-view style="{{'height:'+($root.g0)+';'}}" scroll-y="true" class="data-v-4218a3ca"><view class="u-dropdown-item__options data-v-4218a3ca"><u-cell-group vue-id="24c656d6-1" class="data-v-4218a3ca" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell-item vue-id="{{('24c656d6-2-'+index)+','+('24c656d6-1')}}" arrow="{{false}}" title="{{item.$orig.label}}" title-style="{{item.a0}}" data-event-opts="{{[['^click',[['cellClick',['$0'],[[['options','',index,'value']]]]]]]}}" bind:click="__e" class="data-v-4218a3ca" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{value==item.$orig.value}}"><u-icon vue-id="{{('24c656d6-3-'+index)+','+('24c656d6-2-'+index)}}" name="checkbox-mark" color="{{activeColor}}" size="32" class="data-v-4218a3ca" bind:__l="__l"></u-icon></block></u-cell-item></block></u-cell-group></view></scroll-view></block></block><block wx:else><slot></slot></block></view></block>