(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uview-ui/components/u-loadmore/u-loadmore"],{"51bc":function(t,e,o){"use strict";o.r(e);var n=o("e9bb"),i=o("db03");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);o("75a8");var u=o("f0c5"),a=Object(u["a"])(i["default"],n["b"],n["c"],!1,null,"f8168008",null,!1,n["a"],void 0);e["default"]=a.exports},"75a8":function(t,e,o){"use strict";var n=o("bcd3"),i=o.n(n);i.a},acfc:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=n},bcd3:function(t,e,o){},db03:function(t,e,o){"use strict";o.r(e);var n=o("acfc"),i=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},e9bb:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return n}));var n={uLine:function(){return o.e("uview-ui/components/u-line/u-line").then(o.bind(null,"9db5"))},uLoading:function(){return o.e("uview-ui/components/u-loading/u-loading").then(o.bind(null,"0783"))}},i=function(){var t=this.$createElement,e=(this._self._c,this.$u.addUnit(this.height)),o=this.__get_style([this.loadTextStyle]);this.$mp.data=Object.assign({},{$root:{g0:e,s0:o}})},r=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uview-ui/components/u-loadmore/u-loadmore-create-component',
    {
        'uview-ui/components/u-loadmore/u-loadmore-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("51bc"))
        })
    },
    [['uview-ui/components/u-loadmore/u-loadmore-create-component']]
]);
