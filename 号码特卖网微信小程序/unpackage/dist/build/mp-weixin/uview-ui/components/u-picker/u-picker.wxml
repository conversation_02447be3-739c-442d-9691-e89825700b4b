<u-popup vue-id="37065306-1" maskCloseAble="{{maskCloseAble}}" mode="bottom" popup="{{false}}" length="auto" safeAreaInsetBottom="{{safeAreaInsetBottom}}" z-index="{{uZIndex}}" value="{{value}}" data-event-opts="{{[['^close',[['close']]],['^input',[['__set_model',['','value','$event',[]]]]]]}}" bind:close="__e" bind:input="__e" class="data-v-05acf20c" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-datetime-picker data-v-05acf20c"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="u-picker-header data-v-05acf20c" catchtouchmove="__e"><view class="u-btn-picker u-btn-picker--tips data-v-05acf20c" style="{{'color:'+(cancelColor)+';'}}" hover-class="u-opacity" hover-stay-time="{{150}}" data-event-opts="{{[['tap',[['getResult',['cancel']]]]]}}" bindtap="__e">{{cancelText}}</view><view class="u-picker__title data-v-05acf20c">{{title}}</view><view class="u-btn-picker u-btn-picker--primary data-v-05acf20c" style="{{'color:'+(moving?cancelColor:confirmColor)+';'}}" hover-class="u-opacity" hover-stay-time="{{150}}" data-event-opts="{{[['touchmove',[['',['$event']]]],['tap',[['getResult',['confirm']]]]]}}" catchtouchmove="__e" catchtap="__e">{{''+confirmText+''}}</view></view><view class="u-picker-body data-v-05acf20c"><block wx:if="{{mode=='region'}}"><picker-view class="u-picker-view data-v-05acf20c" value="{{valueArr}}" data-event-opts="{{[['change',[['change',['$event']]]],['pickstart',[['pickstart',['$event']]]],['pickend',[['pickend',['$event']]]]]}}" bindchange="__e" bindpickstart="__e" bindpickend="__e"><block wx:if="{{!reset&&params.province}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{provinces}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c"><view class="u-line-1 data-v-05acf20c">{{item.label}}</view></view></block></picker-view-column></block><block wx:if="{{!reset&&params.city}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{citys}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c"><view class="u-line-1 data-v-05acf20c">{{item.label}}</view></view></block></picker-view-column></block><block wx:if="{{!reset&&params.area}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{areas}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c"><view class="u-line-1 data-v-05acf20c">{{item.label}}</view></view></block></picker-view-column></block></picker-view></block><block wx:else><block wx:if="{{mode=='time'}}"><picker-view class="u-picker-view data-v-05acf20c" value="{{valueArr}}" data-event-opts="{{[['change',[['change',['$event']]]],['pickstart',[['pickstart',['$event']]]],['pickend',[['pickend',['$event']]]]]}}" bindchange="__e" bindpickstart="__e" bindpickend="__e"><block wx:if="{{!reset&&params.year}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{years}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c">{{''+item+''}}<block wx:if="{{showTimeTag}}"><text class="u-text data-v-05acf20c">年</text></block></view></block></picker-view-column></block><block wx:if="{{!reset&&params.month}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c">{{''+item.m0+''}}<block wx:if="{{showTimeTag}}"><text class="u-text data-v-05acf20c">月</text></block></view></block></picker-view-column></block><block wx:if="{{!reset&&params.day}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c">{{''+item.m1+''}}<block wx:if="{{showTimeTag}}"><text class="u-text data-v-05acf20c">日</text></block></view></block></picker-view-column></block><block wx:if="{{!reset&&params.hour}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c">{{''+item.m2+''}}<block wx:if="{{showTimeTag}}"><text class="u-text data-v-05acf20c">时</text></block></view></block></picker-view-column></block><block wx:if="{{!reset&&params.minute}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c">{{''+item.m3+''}}<block wx:if="{{showTimeTag}}"><text class="u-text data-v-05acf20c">分</text></block></view></block></picker-view-column></block><block wx:if="{{!reset&&params.second}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c">{{''+item.m4+''}}<block wx:if="{{showTimeTag}}"><text class="u-text data-v-05acf20c">秒</text></block></view></block></picker-view-column></block></picker-view></block><block wx:else><block wx:if="{{mode=='selector'}}"><picker-view class="u-picker-view data-v-05acf20c" value="{{valueArr}}" data-event-opts="{{[['change',[['change',['$event']]]],['pickstart',[['pickstart',['$event']]]],['pickend',[['pickend',['$event']]]]]}}" bindchange="__e" bindpickstart="__e" bindpickend="__e"><block wx:if="{{!reset}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-column-item data-v-05acf20c"><view class="u-line-1 data-v-05acf20c">{{item.m5}}</view></view></block></picker-view-column></block></picker-view></block><block wx:else><block wx:if="{{mode=='multiSelector'}}"><picker-view class="u-picker-view data-v-05acf20c" value="{{valueArr}}" data-event-opts="{{[['change',[['change',['$event']]]],['pickstart',[['pickstart',['$event']]]],['pickend',[['pickend',['$event']]]]]}}" bindchange="__e" bindpickstart="__e" bindpickend="__e"><block wx:for="{{$root.l7}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{!reset}}"><picker-view-column class="data-v-05acf20c"><block wx:for="{{item.l6}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view class="u-column-item data-v-05acf20c"><view class="u-line-1 data-v-05acf20c">{{item1.m6}}</view></view></block></picker-view-column></block></block></picker-view></block></block></block></block></view></view></u-popup>