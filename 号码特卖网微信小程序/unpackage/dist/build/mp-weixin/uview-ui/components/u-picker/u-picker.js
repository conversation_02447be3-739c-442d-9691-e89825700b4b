(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uview-ui/components/u-picker/u-picker"],{"166e":function(t,e,i){},"430f":function(t,e,i){"use strict";i.r(e);var r=i("45ba"),s=i.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);e["default"]=s.a},"45ba":function(t,e,i){"use strict";var r=i("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=r(i("448a")),a=r(i("7037")),n=r(i("77a4")),u=r(i("4844")),o=r(i("ea25")),h={name:"u-picker",props:{params:{type:Object,default:function(){return{year:!0,month:!0,day:!0,hour:!1,minute:!1,second:!1,province:!0,city:!0,area:!0,timestamp:!0}}},range:{type:Array,default:function(){return[]}},defaultSelector:{type:Array,default:function(){return[0]}},rangeKey:{type:String,default:""},mode:{type:String,default:"time"},startYear:{type:[String,Number],default:1950},endYear:{type:[String,Number],default:2050},cancelColor:{type:String,default:"#606266"},confirmColor:{type:String,default:"#2979ff"},defaultTime:{type:String,default:""},defaultRegion:{type:Array,default:function(){return[]}},showTimeTag:{type:Boolean,default:!0},areaCode:{type:Array,default:function(){return[]}},safeAreaInsetBottom:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!0},value:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:0},title:{type:String,default:""},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"}},data:function(){return{years:[],months:[],days:[],hours:[],minutes:[],seconds:[],year:0,month:0,day:0,hour:0,minute:0,second:0,reset:!1,startDate:"",endDate:"",valueArr:[],provinces:n.default,citys:u.default[0],areas:o.default[0][0],province:0,city:0,area:0,moving:!1}},mounted:function(){this.init()},computed:{propsChange:function(){return"".concat(this.mode,"-").concat(this.defaultTime,"-").concat(this.startYear,"-").concat(this.endYear,"-").concat(this.defaultRegion,"-").concat(this.areaCode)},regionChange:function(){return"".concat(this.province,"-").concat(this.city)},yearAndMonth:function(){return"".concat(this.year,"-").concat(this.month)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{propsChange:function(){var t=this;this.reset=!0,setTimeout((function(){return t.init()}),10)},regionChange:function(t){this.citys=u.default[this.province],this.areas=o.default[this.province][this.city]},yearAndMonth:function(t){this.params.year&&this.setDays()},value:function(t){var e=this;t&&(this.reset=!0,setTimeout((function(){return e.init()}),10))}},methods:{pickstart:function(){this.moving=!0},pickend:function(){this.moving=!1},getItemValue:function(t,e){if(this.mode==e)return"object"==(0,a.default)(t)?t[this.rangeKey]:t},formatNumber:function(t){return+t<10?"0"+t:String(t)},generateArray:function(t,e){return t=Number(t),e=Number(e),e=e>t?e:t,(0,s.default)(Array(e+1).keys()).slice(t)},getIndex:function(t,e){var i=t.indexOf(e);return~i?i:0},initTimeValue:function(){var t=this.defaultTime.replace(/\-/g,"/");t=t&&-1==t.indexOf("/")?"2020/01/01 ".concat(t):t;var e=null;e=t?new Date(t):new Date,this.year=e.getFullYear(),this.month=Number(e.getMonth())+1,this.day=e.getDate(),this.hour=e.getHours(),this.minute=e.getMinutes(),this.second=e.getSeconds()},init:function(){this.valueArr=[],this.reset=!1,"time"==this.mode?(this.initTimeValue(),this.params.year&&(this.valueArr.push(0),this.setYears()),this.params.month&&(this.valueArr.push(0),this.setMonths()),this.params.day&&(this.valueArr.push(0),this.setDays()),this.params.hour&&(this.valueArr.push(0),this.setHours()),this.params.minute&&(this.valueArr.push(0),this.setMinutes()),this.params.second&&(this.valueArr.push(0),this.setSeconds())):"region"==this.mode?(this.params.province&&(this.valueArr.push(0),this.setProvinces()),this.params.city&&(this.valueArr.push(0),this.setCitys()),this.params.area&&(this.valueArr.push(0),this.setAreas())):"selector"==this.mode?this.valueArr=this.defaultSelector:"multiSelector"==this.mode&&(this.valueArr=this.defaultSelector,this.multiSelectorValue=this.defaultSelector),this.$forceUpdate()},setYears:function(){this.years=this.generateArray(this.startYear,this.endYear),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.years,this.year))},setMonths:function(){this.months=this.generateArray(1,12),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.months,this.month))},setDays:function(){var t=new Date(this.year,this.month,0).getDate();this.days=this.generateArray(1,t);var e=0;e=this.params.year&&this.params.month?2:this.params.month||this.params.year?1:0,this.day>this.days.length&&(this.day=this.days.length),this.valueArr.splice(e,1,this.getIndex(this.days,this.day))},setHours:function(){this.hours=this.generateArray(0,23),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.hours,this.hour))},setMinutes:function(){this.minutes=this.generateArray(0,59),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.minutes,this.minute))},setSeconds:function(){this.seconds=this.generateArray(0,59),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.seconds,this.second))},setProvinces:function(){if(this.params.province){var t="",e=!1;this.areaCode.length?(t=this.areaCode[0],e=!0):t=this.defaultRegion.length?this.defaultRegion[0]:0,n.default.map((function(i,r){(e?i.value==t:i.label==t)&&(t=r)})),this.province=t,this.provinces=n.default,this.valueArr.splice(0,1,this.province)}},setCitys:function(){if(this.params.city){var t="",e=!1;this.areaCode.length?(t=this.areaCode[1],e=!0):t=this.defaultRegion.length?this.defaultRegion[1]:0,u.default[this.province].map((function(i,r){(e?i.value==t:i.label==t)&&(t=r)})),this.city=t,this.citys=u.default[this.province],this.valueArr.splice(1,1,this.city)}},setAreas:function(){if(this.params.area){var t="",e=!1;this.areaCode.length?(t=this.areaCode[2],e=!0):t=this.defaultRegion.length?this.defaultRegion[2]:0,o.default[this.province][this.city].map((function(i,r){(e?i.value==t:i.label==t)&&(t=r)})),this.area=t,this.areas=o.default[this.province][this.city],this.valueArr.splice(2,1,this.area)}},close:function(){this.$emit("input",!1)},change:function(t){this.valueArr=t.detail.value;var e=0;if("time"==this.mode)this.params.year&&(this.year=this.years[this.valueArr[e++]]),this.params.month&&(this.month=this.months[this.valueArr[e++]]),this.params.day&&(this.day=this.days[this.valueArr[e++]]),this.params.hour&&(this.hour=this.hours[this.valueArr[e++]]),this.params.minute&&(this.minute=this.minutes[this.valueArr[e++]]),this.params.second&&(this.second=this.seconds[this.valueArr[e++]]);else if("region"==this.mode)this.params.province&&(this.province=this.valueArr[e++]),this.params.city&&(this.city=this.valueArr[e++]),this.params.area&&(this.area=this.valueArr[e++]);else if("multiSelector"==this.mode){var i=null;this.defaultSelector.map((function(e,r){e!=t.detail.value[r]&&(i=r)})),null!=i&&this.$emit("columnchange",{column:i,index:t.detail.value[i]})}},getResult:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(!this.moving){var e={};"time"==this.mode?(this.params.year&&(e.year=this.formatNumber(this.year||0)),this.params.month&&(e.month=this.formatNumber(this.month||0)),this.params.day&&(e.day=this.formatNumber(this.day||0)),this.params.hour&&(e.hour=this.formatNumber(this.hour||0)),this.params.minute&&(e.minute=this.formatNumber(this.minute||0)),this.params.second&&(e.second=this.formatNumber(this.second||0)),this.params.timestamp&&(e.timestamp=this.getTimestamp())):"region"==this.mode?(this.params.province&&(e.province=n.default[this.province]),this.params.city&&(e.city=u.default[this.province][this.city]),this.params.area&&(e.area=o.default[this.province][this.city][this.area])):("selector"==this.mode||"multiSelector"==this.mode)&&(e=this.valueArr),t&&this.$emit(t,e),this.close()}},getTimestamp:function(){var t=this.year+"/"+this.month+"/"+this.day+" "+this.hour+":"+this.minute+":"+this.second;return new Date(t).getTime()/1e3}}};e.default=h},"87cf":function(t,e,i){"use strict";i.r(e);var r=i("db35"),s=i("430f");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(a);i("90c8");var n=i("f0c5"),u=Object(n["a"])(s["default"],r["b"],r["c"],!1,null,"05acf20c",null,!1,r["a"],void 0);e["default"]=u.exports},"90c8":function(t,e,i){"use strict";var r=i("166e"),s=i.n(r);s.a},db35:function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return r}));var r={uPopup:function(){return i.e("uview-ui/components/u-popup/u-popup").then(i.bind(null,"2987"))}},s=function(){var t=this,e=t.$createElement,i=(t._self._c,"region"!=t.mode&&"time"==t.mode&&!t.reset&&t.params.month?t.__map(t.months,(function(e,i){var r=t.__get_orig(e),s=t.formatNumber(e);return{$orig:r,m0:s}})):null),r="region"!=t.mode&&"time"==t.mode&&!t.reset&&t.params.day?t.__map(t.days,(function(e,i){var r=t.__get_orig(e),s=t.formatNumber(e);return{$orig:r,m1:s}})):null,s="region"!=t.mode&&"time"==t.mode&&!t.reset&&t.params.hour?t.__map(t.hours,(function(e,i){var r=t.__get_orig(e),s=t.formatNumber(e);return{$orig:r,m2:s}})):null,a="region"!=t.mode&&"time"==t.mode&&!t.reset&&t.params.minute?t.__map(t.minutes,(function(e,i){var r=t.__get_orig(e),s=t.formatNumber(e);return{$orig:r,m3:s}})):null,n="region"!=t.mode&&"time"==t.mode&&!t.reset&&t.params.second?t.__map(t.seconds,(function(e,i){var r=t.__get_orig(e),s=t.formatNumber(e);return{$orig:r,m4:s}})):null,u="region"==t.mode||"time"==t.mode||"selector"!=t.mode||t.reset?null:t.__map(t.range,(function(e,i){var r=t.__get_orig(e),s=t.getItemValue(e,"selector");return{$orig:r,m5:s}})),o="region"!=t.mode&&"time"!=t.mode&&"selector"!=t.mode&&"multiSelector"==t.mode?t.__map(t.range,(function(e,i){var r=t.__get_orig(e),s=t.reset?null:t.__map(e,(function(e,i){var r=t.__get_orig(e),s=t.getItemValue(e,"multiSelector");return{$orig:r,m6:s}}));return{$orig:r,l6:s}})):null;t.$mp.data=Object.assign({},{$root:{l0:i,l1:r,l2:s,l3:a,l4:n,l5:u,l7:o}})},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uview-ui/components/u-picker/u-picker-create-component',
    {
        'uview-ui/components/u-picker/u-picker-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("87cf"))
        })
    },
    [['uview-ui/components/u-picker/u-picker-create-component']]
]);
