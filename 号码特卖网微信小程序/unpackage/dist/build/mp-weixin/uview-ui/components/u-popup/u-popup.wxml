<block wx:if="{{visibleSync}}"><view class="u-drawer data-v-1a01ef45" style="{{$root.s0}}" hover-stop-propagation="{{true}}"><u-mask vue-id="3fc9d948-1" duration="{{duration}}" custom-style="{{maskCustomStyle}}" maskClickAble="{{maskCloseAble}}" z-index="{{uZindex-2}}" show="{{showDrawer&&mask}}" data-event-opts="{{[['^click',[['maskClick']]]]}}" bind:click="__e" class="data-v-1a01ef45" bind:__l="__l"></u-mask><view data-event-opts="{{[['tap',[['modeCenterClose',['$0'],['mode']],['',['$event']]]],['touchmove',[['',['$event']]]]]}}" class="{{['u-drawer-content','data-v-1a01ef45',safeAreaInsetBottom?'safe-area-inset-bottom':'','u-drawer-'+mode,showDrawer?'u-drawer-content-visible':'',zoom&&mode=='center'?'u-animation-zoom':'']}}" style="{{$root.s1}}" catchtap="__e" catchtouchmove="__e"><block wx:if="{{mode=='center'}}"><view data-event-opts="{{[['tap',[['',['$event']]]],['touchmove',[['',['$event']]]]]}}" class="u-mode-center-box data-v-1a01ef45" style="{{$root.s2}}" catchtap="__e" catchtouchmove="__e"><block wx:if="{{closeable}}"><u-icon class="{{['u-close','data-v-1a01ef45','u-close--'+closeIconPos]}}" vue-id="3fc9d948-2" name="{{closeIcon}}" color="{{closeIconColor}}" size="{{closeIconSize}}" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></block><scroll-view class="u-drawer__scroll-view data-v-1a01ef45" scroll-y="true"><slot></slot></scroll-view></view></block><block wx:else><scroll-view class="u-drawer__scroll-view data-v-1a01ef45" scroll-y="true"><slot></slot></scroll-view></block><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="{{['u-close','data-v-1a01ef45','u-close--'+closeIconPos]}}" bindtap="__e"><block wx:if="{{mode!='center'&&closeable}}"><u-icon vue-id="3fc9d948-3" name="{{closeIcon}}" color="{{closeIconColor}}" size="{{closeIconSize}}" class="data-v-1a01ef45" bind:__l="__l"></u-icon></block></view></view></view></block>