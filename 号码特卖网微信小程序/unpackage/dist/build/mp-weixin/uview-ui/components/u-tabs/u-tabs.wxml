<view class="u-tabs data-v-0ef15e43" style="{{'background:'+(bgColor)+';'}}"><view class="data-v-0ef15e43"><scroll-view class="u-scroll-view data-v-0ef15e43" scroll-x="{{true}}" scroll-left="{{scrollLeft}}" scroll-with-animation="{{true}}"><view class="{{['u-scroll-box','data-v-0ef15e43',(!isScroll)?'u-tabs-scorll-flex':'']}}" id="{{id}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-tab-item u-line-1 data-v-0ef15e43" style="{{item.s0}}" id="{{'u-tab-item-'+index}}" data-event-opts="{{[['tap',[['clickTab',[index]]]]]}}" bindtap="__e"><u-badge vue-id="{{'6ae46646-1-'+index}}" count="{{item.$orig[count]||item.$orig['count']||0}}" offset="{{offset}}" size="mini" class="data-v-0ef15e43" bind:__l="__l"></u-badge>{{''+(item.$orig[name]||item.$orig['name'])+''}}</view></block><block wx:if="{{showBar}}"><view class="u-tab-bar data-v-0ef15e43" style="{{$root.s1}}"></view></block></view></scroll-view></view></view>