(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/iconCenter/allNum/allNum"],{"2fe9":function(e,t,n){"use strict";n.r(t);var u=n("df84"),s=n.n(u);for(var o in u)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return u[e]}))}(o);t["default"]=s.a},"33ef":function(e,t,n){"use strict";var u=n("7702"),s=n.n(u);s.a},3916:function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return u}));var u={uIcon:function(){return n.e("uview-ui/components/u-icon/u-icon").then(n.bind(null,"897d"))},uCheckboxGroup:function(){return Promise.all([n.e("common/vendor"),n.e("uview-ui/components/u-checkbox-group/u-checkbox-group")]).then(n.bind(null,"8eea"))},uCheckbox:function(){return n.e("uview-ui/components/u-checkbox/u-checkbox").then(n.bind(null,"1ae4"))},uDropdown:function(){return n.e("uview-ui/components/u-dropdown/u-dropdown").then(n.bind(null,"36e8"))},uDropdownItem:function(){return n.e("uview-ui/components/u-dropdown-item/u-dropdown-item").then(n.bind(null,"f345"))},uImage:function(){return n.e("uview-ui/components/u-image/u-image").then(n.bind(null,"d3e5"))},uLoadmore:function(){return n.e("uview-ui/components/u-loadmore/u-loadmore").then(n.bind(null,"51bc"))},uBackTop:function(){return n.e("uview-ui/components/u-back-top/u-back-top").then(n.bind(null,"1fea"))}},s=function(){var e=this.$createElement;this._self._c},o=[]},"4aac":function(e,t,n){"use strict";n.r(t);var u=n("3916"),s=n("2fe9");for(var o in s)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(o);n("33ef");var i=n("f0c5"),c=Object(i["a"])(s["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);t["default"]=c.exports},7702:function(e,t,n){},d6d0:function(e,t,n){"use strict";(function(e,t){var u=n("4ea4");n("8b54");u(n("66fd"));var s=u(n("4aac"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(s.default)}).call(this,n("bc2e")["default"],n("543d")["createPage"])},df84:function(e,t,n){"use strict";(function(e){var u=n("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=u(n("6ab2")),o=u(n("1e08")),i=new o.default({key:"RCBBZ-GJTWW-V5PR5-YLXU6-ZKOM5-Z6FGF"}),c={data:function(){return{scrollTop:0,searsh:{searchChangeModleActive:!0,accurateValue:"",accurateLastChecked:!1,inputVagueValue:"",inputVague:[{num:1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1}]},dropdown:{fixedTitle:"全部",moneySelectActiveIndex:0,moneySelectTitle:"价格筛选",moneySelectForm:"",moneySelectList:[{label:"价格不限"},{label:"0-50"},{label:"51-200"},{label:"201-500"},{label:"501-1000"},{label:"大于1000"}],cmccSelectTitle:"网络制式",cmccSelectForm:"网络制式",cmccSelectOptions:[{label:"全部制式",value:"全部制式"},{label:"移动网络",value:"移动网络"},{label:"联通网络",value:"联通网络"},{label:"电信网络",value:"电信网络"}],typeSelectActiveIndex:0,typeSelectTitle:"靓号类型",typeSelectForm:"AAAA",typeSelectList:[{label:"类型不限"},{label:"AAAA"},{label:"AAAAA"},{label:"AAAAAB"},{label:"AAAAAA"},{label:"AAAAAAA"}]},numberObj:{numberList:[]},loading:{status:"loadmore",loadText:{loadmore:"上拉加载更多",loading:"正在加载中...",nomore:"没有更多了"}}}},onLoad:function(t){console.log(t),t.acid?e.setStorageSync("acid",t.acid):e.setStorageSync("acid","490cd1c00cc1492298f85ae005325474"),t.clickid?e.setStorageSync("clickid",t.clickid):e.setStorageSync("clickid","");var n=this;e.getLocation({type:"wgs84",success:function(e){i.reverseGeocoder({location:"".concat(e.latitude,",").concat(e.longitude),success:function(e){console.log(e),n.dropdown.fixedTitle=e.result.address_component.city,n.getNumberList(),setTimeout((function(){n.$refs.indexDropdown.highlight(0)}),500)},fail:function(e){console.error(e)}}),console.log(e)},fail:function(e){console.log(e)}})},onShow:function(t){var n=this;e.getStorageSync("cityName")&&setTimeout((function(){var t=e.getStorageSync("cityName");n.dropdown.fixedTitle=t,e.removeStorageSync("cityName"),n.getNumberList(),n.$refs.indexDropdown.highlight(0)}),200)},onHide:function(e){},onReachBottom:function(){var e=this;console.log("触底了"),this.getNumberListMore(),this.loading.status="loading",setTimeout((function(){e.loading.status="loadmore"}),3e3)},onShareAppMessage:function(e){return{title:"号码特卖网主营全国手机靓号在线选购！",path:"/pages/index/index",templateId:"11e0i0b84a11i1j25a"}},onPageScroll:function(e){this.scrollTop=e.scrollTop},methods:{inputFocusRese:function(){this.searsh.inputVague[1].focus=!1,this.searsh.inputVague[2].focus=!1,this.searsh.inputVague[3].focus=!1,this.searsh.inputVague[4].focus=!1,this.searsh.inputVague[5].focus=!1,this.searsh.inputVague[6].focus=!1,this.searsh.inputVague[7].focus=!1,this.searsh.inputVague[8].focus=!1,this.searsh.inputVague[9].focus=!1,this.searsh.inputVague[10].focus=!1},onKeyInput_1:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[2].focus=!0:this.inputFocusRese()},onKeyInput_2:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[3].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[1].focus=!0)},onKeyInput_3:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[4].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[2].focus=!0)},onKeyInput_4:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[5].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[3].focus=!0,console.log(this.searsh.inputVague))},onKeyInput_5:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[6].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[4].focus=!0)},onKeyInput_6:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[7].focus=!0:this.searsh.inputVague[5].focus=!0},onKeyInput_7:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[8].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[6].focus=!0)},onKeyInput_8:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[9].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[7].focus=!0)},onKeyInput_9:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[10].focus=!0:this.searsh.inputVague[8].focus=!0},onKeyInput_10:function(e){this.inputFocusRese(),e.target.value.length>=1||(this.inputFocusRese(),this.searsh.inputVague[9].focus=!0)},getNumberList:function(){e.showLoading({title:"加载中",mask:!0});var t=this;t.numberObj.numberList=[],console.log("获取号码列表");var n="",u="",o="",i="",c="";switch("全部"!==this.dropdown.fixedTitle&&(n=this.dropdown.fixedTitle),"价格不限"!==this.dropdown.moneySelectForm&&(u=this.dropdown.moneySelectForm),"大于1000"==this.dropdown.moneySelectForm&&(u="1001-1000000"),"类型不限"!==this.dropdown.typeSelectForm&&(o=this.dropdown.typeSelectForm),"全部"==n&&(n=""),c=this.searsh.accurateLastChecked?"y":"",this.dropdown.cmccSelectTitle){case"移动网络":i="移动";break;case"联通网络":i="联通";break;case"电信网络":i="电信";break;default:i="";break}var a={priceStr:u,ruleName:o,province:"",city:n.split("市")[0],searchNum:this.searsh.accurateValue,isEnd:c,telcoType:i,num1:this.searsh.inputVague[1].num,num2:this.searsh.inputVague[2].num,num3:this.searsh.inputVague[3].num,num4:this.searsh.inputVague[4].num,num5:this.searsh.inputVague[5].num,num6:this.searsh.inputVague[6].num,num7:this.searsh.inputVague[7].num,num8:this.searsh.inputVague[8].num,num9:this.searsh.inputVague[9].num,num10:this.searsh.inputVague[10].num};console.log(a),this.$u.api.searchJpNumber(a).then((function(n){if(e.hideLoading(),console.log(n),n){console.log(n);for(var u=[],o=0;o<n.length;o++){var i="";i=n[o].province==n[o].city?"".concat(n[o].city):"".concat(n[o].province).concat(n[o].city);var c={id:"".concat(n[o].id),number:"".concat(s.default.numInitialize(n[o].mobile,n[o].ruleNum)),money:"".concat(n[o].lowPrice),city:"".concat(i),cmcc:"".concat(n[o].telcoType)};u.push(c),t.numberObj.numberList.push(c)}console.log(u)}})).catch((function(e){console.log(e)}))},getNumberListMore:function(){e.showLoading({title:"加载中",mask:!0});var t=this;console.log("获取号码列表");var n="",u="",o="",i="",c="";switch("全部"!==this.dropdown.fixedTitle&&(n=this.dropdown.fixedTitle),"价格不限"!==this.dropdown.moneySelectForm&&(u=this.dropdown.moneySelectForm),"大于1000"==this.dropdown.moneySelectForm&&(u="1001-1000000"),"类型不限"!==this.dropdown.typeSelectForm&&(o=this.dropdown.typeSelectForm),c=this.searsh.accurateLastChecked?"y":"",this.dropdown.cmccSelectTitle){case"移动网络":i="移动";break;case"联通网络":i="联通";break;case"电信网络":i="电信";break;default:i="";break}var a={priceStr:u,ruleName:o,province:"",city:n.split("市")[0],searchNum:this.searsh.accurateValue,isEnd:c,telcoType:i,num1:this.searsh.inputVague[1].num,num2:this.searsh.inputVague[2].num,num3:this.searsh.inputVague[3].num,num4:this.searsh.inputVague[4].num,num5:this.searsh.inputVague[5].num,num6:this.searsh.inputVague[6].num,num7:this.searsh.inputVague[7].num,num8:this.searsh.inputVague[8].num,num9:this.searsh.inputVague[9].num,num10:this.searsh.inputVague[10].num};console.log(a),this.$u.api.searchJpNumber(a).then((function(n){if(e.hideLoading(),console.log(n),n){console.log(n);for(var u=[],o=0;o<n.length;o++){var i="";i=n[o].province==n[o].city?"".concat(n[o].city):"".concat(n[o].province).concat(n[o].city);var c={id:"".concat(n[o].id),number:"".concat(s.default.numInitialize(n[o].mobile,n[o].ruleNum)),money:"".concat(n[o].lowPrice),city:"".concat(i),cmcc:"".concat(n[o].telcoType)};u.push(c),t.numberObj.numberList.push(c)}console.log(u)}}))},getNumberListWugan:function(){var t=this;console.log("获取号码列表");var n="",u="",o="",i="",c="";switch("全部"!==this.dropdown.fixedTitle&&(n=this.dropdown.fixedTitle),"价格不限"!==this.dropdown.moneySelectForm&&(u=this.dropdown.moneySelectForm),"大于1000"==this.dropdown.moneySelectForm&&(u="1001-1000000"),"类型不限"!==this.dropdown.typeSelectForm&&(o=this.dropdown.typeSelectForm),this.dropdown.cmccSelectTitle){case"移动网络":i="移动";break;case"联通网络":i="联通";break;case"电信网络":i="电信";break;default:i="";break}c=this.searsh.accurateLastChecked?"y":"";var a={priceStr:u,ruleName:o,province:"",city:n.split("市")[0],searchNum:this.searsh.accurateValue,isEnd:c,telcoType:i,num1:this.searsh.inputVague[1].num,num2:this.searsh.inputVague[2].num,num3:this.searsh.inputVague[3].num,num4:this.searsh.inputVague[4].num,num5:this.searsh.inputVague[5].num,num6:this.searsh.inputVague[6].num,num7:this.searsh.inputVague[7].num,num8:this.searsh.inputVague[8].num,num9:this.searsh.inputVague[9].num,num10:this.searsh.inputVague[10].num};console.log(a),this.$u.api.searchJpNumber(a).then((function(n){if(e.hideLoading(),console.log(n),n){console.log(n);for(var u=[],o=0;o<n.length;o++){var i="";i=n[o].province==n[o].city?"".concat(n[o].city):"".concat(n[o].province).concat(n[o].city);var c={id:"".concat(n[o].id),number:"".concat(s.default.numInitialize(n[o].mobile,n[o].ruleNum)),money:"".concat(n[o].lowPrice),city:"".concat(i),cmcc:"".concat(n[o].telcoType)};u.push(c),t.numberObj.numberList.push(c)}console.log(u)}}))},modleChange:function(){this.searsh.searchChangeModleActive=!this.searsh.searchChangeModleActive,this.searsh.searchChangeModleActive?(console.log("当前为模糊搜索"),this.searsh.inputVague=[{num:1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1}]):(console.log("当前为精准搜索"),this.searsh.accurateValue="",this.searsh.accurateLastChecked=!1)},inputItemInput_1:function(e){console.log(e.detail.value),1==e.detail.value.length&&(this.searsh.inputVague[2].focus=!0)},searchInputVagueChange:function(e){this.searsh.inputVagueValue=e,console.log(this.searsh.inputVagueValue)},searchButtClick:function(){console.log("搜索模式：".concat(this.searsh.searchChangeModleActive,"、模糊搜索内容：").concat(this.searsh.accurateValue,"、是否尾号：").concat(this.searsh.accurateLastChecked,"、精准搜索内容：").concat(this.searsh.inputVague[0].num).concat(this.searsh.inputVague[1].num).concat(this.searsh.inputVague[2].num).concat(this.searsh.inputVague[3].num).concat(this.searsh.inputVague[4].num).concat(this.searsh.inputVague[5].num).concat(this.searsh.inputVague[6].num).concat(this.searsh.inputVague[7].num).concat(this.searsh.inputVague[8].num).concat(this.searsh.inputVague[9].num).concat(this.searsh.inputVague[10].num)),this.getNumberList()},dropdownOpen:function(e){switch(console.log(e),e){case 0:this.$refs.indexDropdown.close(),this.$u.route({url:"pages/indexList/indexList"});break;default:break}},navCityChange:function(){this.$refs.indexDropdown.highlight(0)},cmccSelectChange:function(e){console.log(e),this.dropdown.cmccSelectTitle=e,this.getNumberList()},tagMoneyClick:function(e){this.$u.toast("".concat(this.dropdown.moneySelectList[e].label)),this.dropdown.moneySelectActiveIndex=e,this.$refs.indexDropdown.close(),this.dropdown.moneySelectTitle=this.dropdown.moneySelectList[e].label,this.dropdown.moneySelectForm=this.dropdown.moneySelectList[e].label,this.getNumberList()},tagTypeClick:function(e){this.$u.toast("".concat(this.dropdown.typeSelectList[e].label)),this.dropdown.typeSelectActiveIndex=e,this.$refs.indexDropdown.close(),this.dropdown.typeSelectTitle=this.dropdown.typeSelectList[e].label,this.dropdown.typeSelectForm=this.dropdown.typeSelectList[e].label,this.getNumberList()},toNumDetail:function(e){this.$u.route({type:"navigateTo",url:"pages/index/numDetail/numDetail",params:{proId:e,isCallBackClickid:!1}})},toIconCenter:function(e){switch(e){case 0:this.$u.route({url:"pages/iconCenter/specialOffer/specialOffer"});break;case 1:break;case 2:this.$u.route({type:"switchTab",url:"pages/hotCard/hotCard"});break;case 3:this.$u.route({url:"pages/iconCenter/personalityNum/personalityNum"});break;default:break}}}};t.default=c}).call(this,n("543d")["default"])}},[["d6d0","common/runtime","common/vendor"]]]);