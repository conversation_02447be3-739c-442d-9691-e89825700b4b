<view class="content"><view class="banner"><image src="https://oss.zjhrnet.com/miniapp/douyin/indexIcon/banner-4-1.jpg" mode="widthFix"></image></view><view class="search"><view class="search-sec"><view class="search-changemodle"><view data-event-opts="{{[['tap',[['modleChange',['$event']]]]]}}" class="search-changemodle-text" bindtap="__e">{{''+(searsh.searchChangeModleActive?'精准':'模糊')+''}}</view><view class="search-changemodle-icon"><u-icon vue-id="4e1d023f-1" name="arrow-right" color="#ccc" size="28" bind:__l="__l"></u-icon></view><view class="search-changemodle-hr"></view></view><view class="search-input"><block wx:if="{{searsh.searchChangeModleActive}}"><view class="search-input-accurate"><view class="search-input-accurate-icon"><u-icon vue-id="4e1d023f-2" name="search" color="#ccc" size="28" bind:__l="__l"></u-icon></view><view class="search-input-accurate-input"><input type="number" placeholder="输入3-8位数字搜索" maxlength="8" clearable="{{false}}" data-event-opts="{{[['input',[['__set_model',['$0','accurateValue','$event',[]],['searsh']]]]]}}" value="{{searsh.accurateValue}}" bindinput="__e"/></view><view class="search-input-accurate-last" size="30"><u-checkbox-group vue-id="4e1d023f-3" bind:__l="__l" vue-slots="{{['default']}}"><u-checkbox bind:input="__e" vue-id="{{('4e1d023f-4')+','+('4e1d023f-3')}}" shape="square" size="30" active-color="#d83337" value="{{searsh.accurateLastChecked}}" data-event-opts="{{[['^input',[['__set_model',['$0','accurateLastChecked','$event',[]],['searsh']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}">尾号</u-checkbox></u-checkbox-group></view></view></block><block wx:else><view class="search-input-vague-2"><view class="inputItem"><input type="number" maxlength="1" placeholder=" " disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n0']]]]]}}" value="{{searsh.inputVague[0].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[1].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n1']],['onKeyInput_1',['$event']]]]]}}" value="{{searsh.inputVague[1].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[2].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n2']],['onKeyInput_2',['$event']]]]]}}" value="{{searsh.inputVague[2].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[3].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n3']],['onKeyInput_3',['$event']]]]]}}" value="{{searsh.inputVague[3].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[4].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n4']],['onKeyInput_4',['$event']]]]]}}" value="{{searsh.inputVague[4].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[5].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n5']],['onKeyInput_5',['$event']]]]]}}" value="{{searsh.inputVague[5].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[6].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n6']],['onKeyInput_6',['$event']]]]]}}" value="{{searsh.inputVague[6].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[7].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n7']],['onKeyInput_7',['$event']]]]]}}" value="{{searsh.inputVague[7].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[8].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n8']],['onKeyInput_8',['$event']]]]]}}" value="{{searsh.inputVague[8].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[9].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n9']],['onKeyInput_9',['$event']]]]]}}" value="{{searsh.inputVague[9].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[10].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n10']],['onKeyInput_10',['$event']]]]]}}" value="{{searsh.inputVague[10].num}}" bindinput="__e"/></view></view></block></view><view data-event-opts="{{[['tap',[['searchButtClick',['$event']]]]]}}" class="search-butt" bindtap="__e">搜索</view></view></view><view class="nav"><u-dropdown class="vue-ref" vue-id="4e1d023f-5" active-color="#d83337" data-ref="indexDropdown" data-event-opts="{{[['^open',[['dropdownOpen']]]]}}" bind:open="__e" bind:__l="__l" vue-slots="{{['default']}}"><u-dropdown-item class="fixedTitle" vue-id="{{('4e1d023f-6')+','+('4e1d023f-5')}}" title="{{dropdown.fixedTitle}}" data-event-opts="{{[['^change',[['navCityChange']]]]}}" bind:change="__e" bind:__l="__l"></u-dropdown-item><u-dropdown-item vue-id="{{('4e1d023f-7')+','+('4e1d023f-5')}}" title="{{dropdown.moneySelectTitle}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content"><view class="item-box"><block wx:for="{{dropdown.moneySelectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tagMoneyClick',[index]]]]]}}" class="{{['item',index==dropdown.moneySelectActiveIndex?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></view></u-dropdown-item><u-dropdown-item vue-id="{{('4e1d023f-8')+','+('4e1d023f-5')}}" title="{{dropdown.cmccSelectTitle}}" options="{{dropdown.cmccSelectOptions}}" value="{{dropdown.cmccSelectForm}}" data-event-opts="{{[['^change',[['cmccSelectChange']]],['^input',[['__set_model',['$0','cmccSelectForm','$event',[]],['dropdown']]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-dropdown-item><u-dropdown-item vue-id="{{('4e1d023f-9')+','+('4e1d023f-5')}}" title="{{dropdown.typeSelectTitle}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content"><view class="item-box"><block wx:for="{{dropdown.typeSelectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tagTypeClick',[index]]]]]}}" class="{{['item',index==dropdown.typeSelectActiveIndex?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></view></u-dropdown-item></u-dropdown></view><view class="main"><block wx:if="{{numberObj.numberList}}"><view class="main-list"><block wx:for="{{numberObj.numberList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toNumDetail',['$0'],[[['numberObj.numberList','',index,'id']]]]]]]}}" class="li" bindtap="__e"><view class="num"><rich-text nodes="{{item.number}}"></rich-text></view><view class="tips"><u-image vue-id="{{'4e1d023f-10-'+index}}" src="https://oss.zjhrnet.com/miniapp/douyin/jp.png" width="58rpx" height="67rpx" mode="widthFix" fade="{{true}}" bind:__l="__l"></u-image></view><view class="money">{{'￥'+item.money+''}}</view><view class="bottom"><view class="city">{{''+item.city+''}}</view><block wx:if="{{item.cmcc=='移动'}}"><view class="cmcc">{{''+item.cmcc+'网络'}}</view></block><block wx:else><block wx:if="{{item.cmcc=='联通'}}"><view class="cmcc zglt">{{''+item.cmcc+'网络'}}</view></block><block wx:else><view class="cmcc zgdx">{{''+item.cmcc+'网络'}}</view></block></block></view></view></block></view></block><view class="loading"><u-loadmore vue-id="4e1d023f-11" status="{{loading.status}}" loadText="{{loading.loadText}}" margin-top="30" bind:__l="__l"></u-loadmore></view></view><block wx:if="{{false}}"><view class="kefu"><button class="butt-contact" open-type="contact"><view class="kefu-main">客服咨询</view></button></view></block><u-back-top vue-id="4e1d023f-12" scroll-top="{{scrollTop}}" top="600" duration="300" bind:__l="__l"></u-back-top></view>