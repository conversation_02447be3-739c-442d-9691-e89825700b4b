<view class="personalityNum"><view class="banner"><image src="{{banner}}" width="100%" height="301rpx" mode="widthFix" fade="{{true}}"></image></view><view class="tabs"><view class="tabs_sec"><u-tabs vue-id="c6cf4ec2-1" list="{{tabList}}" current="{{tabCurrent}}" gutter="40" active-color="#f74683" bg-color="none" data-event-opts="{{[['^change',[['tabChange']]]]}}" bind:change="__e" bind:__l="__l"></u-tabs></view></view><view class="search"><view class="search-sec"><view class="search-changemodle"><view class="search-changemodle-icon"><u-icon vue-id="c6cf4ec2-2" name="search" color="#666" size="30" bind:__l="__l"></u-icon></view><view class="search-changemodle-hr"></view></view><view class="search-input"><block wx:if="{{searsh.searchChangeModleActive}}"><view class="search-input-accurate"><view class="search-input-accurate-input"><input type="number" placeholder="输入手机号码匹配另一半" maxlength="8" clearable="{{false}}" data-event-opts="{{[['input',[['__set_model',['$0','accurateValue','$event',[]],['searsh']]]]]}}" value="{{searsh.accurateValue}}" bindinput="__e"/></view></view></block><block wx:else><view class="search-input-vague-2"><view class="inputItem"><input type="number" maxlength="1" placeholder=" " disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n0']]]]]}}" value="{{searsh.inputVague[0].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[1].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n1']],['onKeyInput_1',['$event']]]]]}}" value="{{searsh.inputVague[1].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[2].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n2']],['onKeyInput_2',['$event']]]]]}}" value="{{searsh.inputVague[2].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[3].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n3']],['onKeyInput_3',['$event']]]]]}}" value="{{searsh.inputVague[3].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[4].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n4']],['onKeyInput_4',['$event']]]]]}}" value="{{searsh.inputVague[4].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[5].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n5']],['onKeyInput_5',['$event']]]]]}}" value="{{searsh.inputVague[5].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[6].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n6']],['onKeyInput_6',['$event']]]]]}}" value="{{searsh.inputVague[6].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[7].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n7']],['onKeyInput_7',['$event']]]]]}}" value="{{searsh.inputVague[7].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[8].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n8']],['onKeyInput_8',['$event']]]]]}}" value="{{searsh.inputVague[8].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[9].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n9']],['onKeyInput_9',['$event']]]]]}}" value="{{searsh.inputVague[9].num}}" bindinput="__e"/></view><view class="inputItem"><input type="number" maxlength="1" placeholder=" " focus="{{searsh.inputVague[10].focus}}" data-event-opts="{{[['input',[['__set_model',['$0','num','$event',[]],['searsh.inputVague.__$n10']],['onKeyInput_10',['$event']]]]]}}" value="{{searsh.inputVague[10].num}}" bindinput="__e"/></view></view></block></view><view data-event-opts="{{[['tap',[['searchButtClick',['$event']]]]]}}" class="search-butt" bindtap="__e">搜索</view></view></view><view class="recommend"><block wx:if="{{tabCurrent==1}}"><view class="recommendBirthday"><view data-event-opts="{{[['tap',[['recommendClick',['2023']]]]]}}" class="li" bindtap="__e">2023</view><view data-event-opts="{{[['tap',[['recommendClick',['2022']]]]]}}" class="li" bindtap="__e">2022</view><view data-event-opts="{{[['tap',[['recommendClick',['2021']]]]]}}" class="li" bindtap="__e">2021</view><view data-event-opts="{{[['tap',[['recommendClick',['2020']]]]]}}" class="li" bindtap="__e">2020</view></view></block><block wx:if="{{tabCurrent==2}}"><view class="recommendBirthday"><view data-event-opts="{{[['tap',[['recommendClick',['520']]]]]}}" class="li" bindtap="__e">520</view><view data-event-opts="{{[['tap',[['recommendClick',['521']]]]]}}" class="li" bindtap="__e">521</view><view data-event-opts="{{[['tap',[['recommendClick',['1314']]]]]}}" class="li" bindtap="__e">1314</view><view data-event-opts="{{[['tap',[['recommendClick',['1413']]]]]}}" class="li" bindtap="__e">1413</view></view></block></view><view class="nav"><u-dropdown class="vue-ref" vue-id="c6cf4ec2-3" active-color="#d83337" data-ref="indexDropdown" data-event-opts="{{[['^open',[['dropdownOpen']]]]}}" bind:open="__e" bind:__l="__l" vue-slots="{{['default']}}"><u-dropdown-item class="fixedTitle" vue-id="{{('c6cf4ec2-4')+','+('c6cf4ec2-3')}}" title="{{dropdown.fixedTitle}}" data-event-opts="{{[['^change',[['navCityChange']]]]}}" bind:change="__e" bind:__l="__l"></u-dropdown-item><u-dropdown-item vue-id="{{('c6cf4ec2-5')+','+('c6cf4ec2-3')}}" title="{{dropdown.moneySelectTitle}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content"><view class="item-box"><block wx:for="{{dropdown.moneySelectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tagMoneyClick',[index]]]]]}}" class="{{['item',index==dropdown.moneySelectActiveIndex?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></view></u-dropdown-item><u-dropdown-item vue-id="{{('c6cf4ec2-6')+','+('c6cf4ec2-3')}}" title="{{dropdown.cmccSelectTitle}}" options="{{dropdown.cmccSelectOptions}}" value="{{dropdown.cmccSelectForm}}" data-event-opts="{{[['^change',[['cmccSelectChange']]],['^input',[['__set_model',['$0','cmccSelectForm','$event',[]],['dropdown']]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-dropdown-item><u-dropdown-item vue-id="{{('c6cf4ec2-7')+','+('c6cf4ec2-3')}}" title="{{dropdown.typeSelectTitle}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content"><view class="item-box"><block wx:for="{{dropdown.typeSelectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tagTypeClick',[index]]]]]}}" class="{{['item',index==dropdown.typeSelectActiveIndex?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></view></u-dropdown-item></u-dropdown></view><view class="main"><block wx:if="{{numberObj.numberList}}"><view class="main-list"><block wx:for="{{numberObj.numberList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toNumDetail',['$0'],[[['numberObj.numberList','',index,'id']]]]]]]}}" class="li" bindtap="__e"><view class="num"><rich-text nodes="{{item.number}}"></rich-text></view><view class="money">{{'￥'+item.money+''}}</view><view class="bottom"><view class="city">{{''+item.city+''}}</view><block wx:if="{{item.cmcc=='移动'}}"><view class="cmcc">{{''+item.cmcc+'网络'}}</view></block><block wx:else><block wx:if="{{item.cmcc=='联通'}}"><view class="cmcc zglt">{{''+item.cmcc+'网络'}}</view></block><block wx:else><view class="cmcc zgdx">{{''+item.cmcc+'网络'}}</view></block></block></view></view></block></view></block><view class="loading"><u-loadmore vue-id="c6cf4ec2-8" status="{{loading.status}}" loadText="{{loading.loadText}}" margin-top="30" bind:__l="__l"></u-loadmore></view></view><block wx:if="{{false}}"><view class="kefu"><button class="butt-contact" open-type="contact"><view class="kefu-main">客服咨询</view></button></view></block></view>