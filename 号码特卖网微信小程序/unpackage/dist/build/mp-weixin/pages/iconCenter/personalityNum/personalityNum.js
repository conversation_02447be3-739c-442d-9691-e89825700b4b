(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/iconCenter/personalityNum/personalityNum"],{"29a4":function(e,t,n){"use strict";n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return s}));var s={uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("uview-ui/components/u-tabs/u-tabs")]).then(n.bind(null,"bb8d"))},uIcon:function(){return n.e("uview-ui/components/u-icon/u-icon").then(n.bind(null,"897d"))},uDropdown:function(){return n.e("uview-ui/components/u-dropdown/u-dropdown").then(n.bind(null,"36e8"))},uDropdownItem:function(){return n.e("uview-ui/components/u-dropdown-item/u-dropdown-item").then(n.bind(null,"f345"))},uLoadmore:function(){return n.e("uview-ui/components/u-loadmore/u-loadmore").then(n.bind(null,"51bc"))}},u=function(){var e=this.$createElement;this._self._c},i=[]},"2cc5":function(e,t,n){"use strict";n.r(t);var s=n("6dcc"),u=n.n(s);for(var i in s)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(i);t["default"]=u.a},"33ab":function(e,t,n){"use strict";(function(e,t){var s=n("4ea4");n("8b54");s(n("66fd"));var u=s(n("5e51"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(u.default)}).call(this,n("bc2e")["default"],n("543d")["createPage"])},5904:function(e,t,n){},"5e51":function(e,t,n){"use strict";n.r(t);var s=n("29a4"),u=n("2cc5");for(var i in u)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return u[e]}))}(i);n("a5da");var o=n("f0c5"),a=Object(o["a"])(u["default"],s["b"],s["c"],!1,null,null,null,!1,s["a"],void 0);t["default"]=a.exports},"6dcc":function(e,t,n){"use strict";(function(e){var s=n("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=s(n("6ab2")),i={data:function(){return{banner:"https://oss.zjhrnet.com/miniapp/douyin/indexIcon/banner-1.png",tabList:[{name:"智能推荐"},{name:"生日号码"},{name:"情侣号码"}],tabCurrent:0,searsh:{searchChangeModleActive:!0,accurateValue:"",accurateLastChecked:!1,inputVagueValue:"",inputVague:[{num:1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1}]},dropdown:{fixedTitle:"全部",moneySelectActiveIndex:0,moneySelectTitle:"价格筛选",moneySelectForm:"",moneySelectList:[{label:"价格不限"},{label:"0-50"},{label:"51-200"},{label:"201-500"},{label:"501-1000"},{label:"大于1000"}],cmccSelectTitle:"网络制式",cmccSelectForm:"网络制式",cmccSelectOptions:[{label:"全部制式",value:"全部制式"},{label:"移动网络",value:"移动网络"},{label:"联通网络",value:"联通网络"},{label:"电信网络",value:"电信网络"}],typeSelectActiveIndex:0,typeSelectTitle:"靓号类型",typeSelectForm:"",typeSelectList:[{label:"类型不限"},{label:"AAA"},{label:"AABB"},{label:"AAAA"},{label:"AAAB"},{label:"AAAAB"},{label:"ABAB"},{label:"ABC"},{label:"ABCD"},{label:"ABCDE"},{label:"AAAAA"},{label:"AAAAAB"},{label:"AAAABB"},{label:"AABBCC"},{label:"AAABBB"}]},numberObj:{numberList:[]},loading:{status:"loadmore",loadText:{loadmore:"上拉加载更多",loading:"正在加载中...",nomore:"没有更多了"}}}},onLoad:function(t){console.log(t),t.acid?e.setStorageSync("acid",t.acid):e.setStorageSync("acid","490cd1c00cc1492298f85ae005325474"),t.clickid?e.setStorageSync("clickid",t.clickid):e.setStorageSync("clickid","");this.dropdown.fixedTitle="北京",this.getNumberList()},onShow:function(t){var n=this;e.getStorageSync("cityName")&&setTimeout((function(){var t=e.getStorageSync("cityName");n.dropdown.fixedTitle=t,e.removeStorageSync("cityName"),n.getNumberList(),n.$refs.indexDropdown.highlight(0)}),200)},onHide:function(e){},onReachBottom:function(){var e=this;console.log("触底了"),this.getNumberListMore(),this.loading.status="loading",setTimeout((function(){e.loading.status="loadmore"}),3e3)},onShareAppMessage:function(e){return{title:"号码特卖网主营全国手机靓号在线选购！",path:"/pages/index/index",templateId:"11e0i0b84a11i1j25a"}},methods:{inputFocusRese:function(){this.searsh.inputVague[1].focus=!1,this.searsh.inputVague[2].focus=!1,this.searsh.inputVague[3].focus=!1,this.searsh.inputVague[4].focus=!1,this.searsh.inputVague[5].focus=!1,this.searsh.inputVague[6].focus=!1,this.searsh.inputVague[7].focus=!1,this.searsh.inputVague[8].focus=!1,this.searsh.inputVague[9].focus=!1,this.searsh.inputVague[10].focus=!1},onKeyInput_1:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[2].focus=!0:this.inputFocusRese()},onKeyInput_2:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[3].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[1].focus=!0)},onKeyInput_3:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[4].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[2].focus=!0)},onKeyInput_4:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[5].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[3].focus=!0,console.log(this.searsh.inputVague))},onKeyInput_5:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[6].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[4].focus=!0)},onKeyInput_6:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[7].focus=!0:this.searsh.inputVague[5].focus=!0},onKeyInput_7:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[8].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[6].focus=!0)},onKeyInput_8:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[9].focus=!0:(this.inputFocusRese(),this.searsh.inputVague[7].focus=!0)},onKeyInput_9:function(e){this.inputFocusRese(),e.target.value.length>=1?this.searsh.inputVague[10].focus=!0:this.searsh.inputVague[8].focus=!0},onKeyInput_10:function(e){this.inputFocusRese(),e.target.value.length>=1||(this.inputFocusRese(),this.searsh.inputVague[9].focus=!0)},getNumberList:function(){e.showLoading({title:"加载中",mask:!0});var t=this;t.numberObj.numberList=[],console.log("获取号码列表");var n="",s="",i="",o="",a="";switch("全部"!==this.dropdown.fixedTitle&&(n=this.dropdown.fixedTitle),"价格不限"!==this.dropdown.moneySelectForm&&(s=this.dropdown.moneySelectForm),"大于1000"==this.dropdown.moneySelectForm&&(s="1001-1000000"),"类型不限"!==this.dropdown.typeSelectForm&&(i=this.dropdown.typeSelectForm),"全部"==n&&(n=""),a=this.searsh.accurateLastChecked?"y":"",this.dropdown.cmccSelectTitle){case"移动网络":o="移动";break;case"联通网络":o="联通";break;case"电信网络":o="电信";break;default:o="";break}var c={priceStr:s,ruleName:i,province:"",city:n.split("市")[0],searchNum:this.searsh.accurateValue,isEnd:a,telcoType:o,num1:this.searsh.inputVague[1].num,num2:this.searsh.inputVague[2].num,num3:this.searsh.inputVague[3].num,num4:this.searsh.inputVague[4].num,num5:this.searsh.inputVague[5].num,num6:this.searsh.inputVague[6].num,num7:this.searsh.inputVague[7].num,num8:this.searsh.inputVague[8].num,num9:this.searsh.inputVague[9].num,num10:this.searsh.inputVague[10].num};console.log(c),this.$u.api.searchNumber(c).then((function(n){if(e.hideLoading(),console.log(n),n){console.log(n);for(var s=[],i=0;i<n.length;i++){var o="";o=n[i].province==n[i].city?"".concat(n[i].city):"".concat(n[i].province).concat(n[i].city);var a={id:"".concat(n[i].id),number:"".concat(u.default.numInitialize(n[i].mobile,n[i].ruleNum)),money:"".concat(n[i].lowPrice),city:"".concat(o),cmcc:"".concat(n[i].telcoType)};s.push(a),t.numberObj.numberList.push(a)}console.log(s),t.getNumberListWugan()}})).catch((function(e){console.log(e)}))},getNumberListMore:function(){e.showLoading({title:"加载中",mask:!0});var t=this;console.log("获取号码列表");var n="",s="",i="",o="",a="";switch("全部"!==this.dropdown.fixedTitle&&(n=this.dropdown.fixedTitle),"价格不限"!==this.dropdown.moneySelectForm&&(s=this.dropdown.moneySelectForm),"大于1000"==this.dropdown.moneySelectForm&&(s="1001-1000000"),"类型不限"!==this.dropdown.typeSelectForm&&(i=this.dropdown.typeSelectForm),a=this.searsh.accurateLastChecked?"y":"",this.dropdown.cmccSelectTitle){case"移动网络":o="移动";break;case"联通网络":o="联通";break;case"电信网络":o="电信";break;default:o="";break}var c={priceStr:s,ruleName:i,province:"",city:n.split("市")[0],searchNum:this.searsh.accurateValue,isEnd:a,telcoType:o,num1:this.searsh.inputVague[1].num,num2:this.searsh.inputVague[2].num,num3:this.searsh.inputVague[3].num,num4:this.searsh.inputVague[4].num,num5:this.searsh.inputVague[5].num,num6:this.searsh.inputVague[6].num,num7:this.searsh.inputVague[7].num,num8:this.searsh.inputVague[8].num,num9:this.searsh.inputVague[9].num,num10:this.searsh.inputVague[10].num};console.log(c),this.$u.api.searchNumber(c).then((function(n){if(e.hideLoading(),console.log(n),n){console.log(n);for(var s=[],i=0;i<n.length;i++){var o="";o=n[i].province==n[i].city?"".concat(n[i].city):"".concat(n[i].province).concat(n[i].city);var a={id:"".concat(n[i].id),number:"".concat(u.default.numInitialize(n[i].mobile,n[i].ruleNum)),money:"".concat(n[i].lowPrice),city:"".concat(o),cmcc:"".concat(n[i].telcoType)};s.push(a),t.numberObj.numberList.push(a)}console.log(s),t.getNumberListWugan()}}))},getNumberListWugan:function(){var t=this;console.log("获取号码列表");var n="",s="",i="",o="",a="";switch("全部"!==this.dropdown.fixedTitle&&(n=this.dropdown.fixedTitle),"价格不限"!==this.dropdown.moneySelectForm&&(s=this.dropdown.moneySelectForm),"大于1000"==this.dropdown.moneySelectForm&&(s="1001-1000000"),"类型不限"!==this.dropdown.typeSelectForm&&(i=this.dropdown.typeSelectForm),this.dropdown.cmccSelectTitle){case"移动网络":o="移动";break;case"联通网络":o="联通";break;case"电信网络":o="电信";break;default:o="";break}a=this.searsh.accurateLastChecked?"y":"";var c={priceStr:s,ruleName:i,province:"",city:n.split("市")[0],searchNum:this.searsh.accurateValue,isEnd:a,telcoType:o,num1:this.searsh.inputVague[1].num,num2:this.searsh.inputVague[2].num,num3:this.searsh.inputVague[3].num,num4:this.searsh.inputVague[4].num,num5:this.searsh.inputVague[5].num,num6:this.searsh.inputVague[6].num,num7:this.searsh.inputVague[7].num,num8:this.searsh.inputVague[8].num,num9:this.searsh.inputVague[9].num,num10:this.searsh.inputVague[10].num};console.log(c),this.$u.api.searchNumber(c).then((function(n){if(e.hideLoading(),console.log(n),n){console.log(n);for(var s=[],i=0;i<n.length;i++){var o="";o=n[i].province==n[i].city?"".concat(n[i].city):"".concat(n[i].province).concat(n[i].city);var a={id:"".concat(n[i].id),number:"".concat(u.default.numInitialize(n[i].mobile,n[i].ruleNum)),money:"".concat(n[i].lowPrice),city:"".concat(o),cmcc:"".concat(n[i].telcoType)};s.push(a),t.numberObj.numberList.push(a)}console.log(s)}}))},modleChange:function(){this.searsh.searchChangeModleActive=!this.searsh.searchChangeModleActive,this.searsh.searchChangeModleActive?(console.log("当前为模糊搜索"),this.searsh.inputVague=[{num:1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1},{num:"",focus:!1}]):(console.log("当前为精准搜索"),this.searsh.accurateValue="",this.searsh.accurateLastChecked=!1)},inputItemInput_1:function(e){console.log(e.detail.value),1==e.detail.value.length&&(this.searsh.inputVague[2].focus=!0)},searchInputVagueChange:function(e){this.searsh.inputVagueValue=e,console.log(this.searsh.inputVagueValue)},searchButtClick:function(){console.log("搜索模式：".concat(this.searsh.searchChangeModleActive,"、模糊搜索内容：").concat(this.searsh.accurateValue,"、是否尾号：").concat(this.searsh.accurateLastChecked,"、精准搜索内容：").concat(this.searsh.inputVague[0].num).concat(this.searsh.inputVague[1].num).concat(this.searsh.inputVague[2].num).concat(this.searsh.inputVague[3].num).concat(this.searsh.inputVague[4].num).concat(this.searsh.inputVague[5].num).concat(this.searsh.inputVague[6].num).concat(this.searsh.inputVague[7].num).concat(this.searsh.inputVague[8].num).concat(this.searsh.inputVague[9].num).concat(this.searsh.inputVague[10].num)),this.getNumberList()},dropdownOpen:function(e){switch(console.log(e),e){case 0:this.$refs.indexDropdown.close(),this.$u.route({url:"pages/indexList/indexList"});break;default:break}},navCityChange:function(){this.$refs.indexDropdown.highlight(0)},cmccSelectChange:function(e){console.log(e),this.dropdown.cmccSelectTitle=e,this.getNumberList()},tagMoneyClick:function(e){this.$u.toast("".concat(this.dropdown.moneySelectList[e].label)),this.dropdown.moneySelectActiveIndex=e,this.$refs.indexDropdown.close(),this.dropdown.moneySelectTitle=this.dropdown.moneySelectList[e].label,this.dropdown.moneySelectForm=this.dropdown.moneySelectList[e].label,this.getNumberList()},tagTypeClick:function(e){this.$u.toast("".concat(this.dropdown.typeSelectList[e].label)),this.dropdown.typeSelectActiveIndex=e,this.$refs.indexDropdown.close(),this.dropdown.typeSelectTitle=this.dropdown.typeSelectList[e].label,this.dropdown.typeSelectForm=this.dropdown.typeSelectList[e].label,this.getNumberList()},toNumDetail:function(e){this.$u.route({type:"navigateTo",url:"pages/index/numDetail/numDetail",params:{proId:e,isCallBackClickid:!1}})},tabChange:function(e){switch(console.log(e),this.searsh.accurateValue="",this.tabCurrent=e,e){case 0:this.banner="https://oss.zjhrnet.com/miniapp/douyin/indexIcon/banner-1.png";break;case 1:this.banner="https://oss.zjhrnet.com/miniapp/douyin/indexIcon/banner-2.png";break;case 2:this.banner="https://oss.zjhrnet.com/miniapp/douyin/indexIcon/banner-1.png";break;default:break}},recommendClick:function(e){console.log(e),this.searsh.accurateValue=e,this.getNumberList()}}};t.default=i}).call(this,n("543d")["default"])},a5da:function(e,t,n){"use strict";var s=n("5904"),u=n.n(s);u.a}},[["33ab","common/runtime","common/vendor"]]]);