(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/hotCard/guangdian192G/guangdian192G"],{"1bbc":function(t,e,n){"use strict";n.r(e);var p=n("8f94"),r=n.n(p);for(var o in p)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return p[t]}))}(o);e["default"]=r.a},"3e41":function(t,e,n){"use strict";(function(t,e){var p=n("4ea4");n("8b54");p(n("66fd"));var r=p(n("608d"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(r.default)}).call(this,n("bc2e")["default"],n("543d")["createPage"])},"608d":function(t,e,n){"use strict";n.r(e);var p=n("9631"),r=n("1bbc");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("8f93");var i=n("f0c5"),s=Object(i["a"])(r["default"],p["b"],p["c"],!1,null,null,null,!1,p["a"],void 0);e["default"]=s.exports},"6d91":function(t,e,n){},"8f93":function(t,e,n){"use strict";var p=n("6d91"),r=n.n(p);r.a},"8f94":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{form:{name:"",card:"",tel:"",provice:"",city:"",area:"",address:"",more:"",region:""},pickerShow:!1,agreementChecked:!1,popupShow:!1,agreementTitle:"",agreementInfo:"",tipsMore:"<p>1、套餐29元/月，包含30GB通用流量+30GB定向流量。首充100元每月赠送通用流量132GB，连续赠送24个月，每月共计192G流量。首充100元，赠送50元话费，激活次月开始分5个月月初返还，每月赠送10元抵扣套餐费。</p>\n\t\t\t<p>2、用户入网首月送29元体验金，按天折算抵扣套餐费，体验金仅当月有效;六个月离网需返还第一个月抵扣费用</p>\n\t\t\t<p>3、套餐内通用流量可结转、可共享、不可转赠。专属流量不可结转不可共享、不可转赠。</p>\n\t\t\t<p>4、套餐外计费规则: 套餐外流量按照5元/GB计费，套餐外国内主叫按0.1元/分钟计费，国内被叫免费。国内短信按0.1元/条计费。5、套餐和活动仅供新用户入网; 不充值不能用语音和短信，需充值50元以上可以使用语音和短信</p>"}},methods:{placeOrder:function(){this.agreementChecked=!0,this.$u.test.isEmpty(this.form.name)?this.$refs.uToast.show({title:"姓名错误",type:"error"}):this.$u.test.idCard(this.form.card)?this.$u.test.mobile(this.form.tel)?this.$u.test.isEmpty(this.form.provice)||this.$u.test.isEmpty(this.form.city)||this.$u.test.isEmpty(this.form.area)?this.$refs.uToast.show({title:"请选择城市",type:"error"}):this.$u.test.isEmpty(this.form.address)?this.$refs.uToast.show({title:"详细地址错误",type:"error"}):(this.agreementChecked||this.$refs.uToast.show({title:"请勾选协议",type:"error"}),this.pushForm()):this.$refs.uToast.show({title:"手机号码错误",type:"error"}):this.$refs.uToast.show({title:"身份证错误",type:"error"})},pushForm:function(){var e,n=this;t.showLoading({title:"下单中",mask:!0}),console.log("数据开始推送"),console.log(this.form),this.showMask=!0,e=t.getStorageSync("acid")?t.getStorageSync("acid"):"490cd1c00cc1492298f85ae005325474";var p={acid:e,pid:"2c98ef5f876ba48301876fa5d795001a",username:this.form.name,userphone:this.form.tel,userNo:this.form.card,provice:this.form.provice,city:this.form.city,area:this.form.area,address:this.form.address};console.log(p),this.$u.api.createOrderRandomNumber(p).then((function(t){console.log(t),n.$refs.uToast.show({title:"下单成功",type:"success"}),n.form={name:"",card:"",tel:"",provice:"",city:"",area:"",address:"",more:"",region:""},n.$u.route({url:"pages/hotCard/success/success"})})).catch((function(t){n.$refs.uToast.show({title:t.error,type:"error"})}))},toAgreement:function(t){switch(console.log(t),t){case 1:this.agreementTitle="入网许可协议",this.agreementInfo="<p>甲方与乙方经自愿协商一致，就电信服务的相关事宜达成如下协议：</p>\n\t\t\t\t\t<p>一、 服务内容\n\t\t\t\t\t</p><p>1. 甲方自主选择乙方提供的电信服务，乙方在通信网络覆盖范围内，按照国家颃布的 《电信服务规范》标准为甲方有偿提供其所选择的电信服务。\n\t\t\t\t\t</p><p>2. 电信服务费用根据政府主管部门批准或备案的资费标准及双方的约定执行。计费周 期为自然月，即每月1日0时至当月最后一曰24时。\n\t\t\t\t\t</p><p>3. 乙方提供标准资费及各类套餐资费供甲方使用。如选择套餐资费，月通信消费量未超过套餐约定包含的内容及使用量时收取套餐基本费；月通信消费量超出套餐约定包含的内容或使用量时收取套餐基本费及超出费用。\n\t\t\t\t\t</p><p>二、 甲方的权利与义务\n\t\t\t\t\t</p><p>1. 甲方有权自主选择乙方提供的各类电信服务，有权选择符合国家入网许可规定的终端设备，并在双方约定的电信服务范围内使用。\n\t\t\t\t\t</p><p>2. 甲方或其委托人办理电信业务时，应提供真实有效的信息资料，并配合乙方进行核实；对身份不明或拒绝身份查验的，乙方有权拒绝提供服务。协议有效期内，甲方登记资料如有变更，应及时办理变更手续。\n\t\t\t\t\t</p><p>3. 甲方应按照与乙方约定的时间和方式，及时足额向乙方支付电信服务费用，否则乙方有权停止电信服务。\n\t\t\t\t\t</p><p>4. 甲方可对暂不使用的电信服务申谓停机，若为套餐资费应将套餐中包含的所有电信服务同时停机。\n\t\t\t\t\t</p><p>5. 甲方负责其自备的终端设备或线路的安装与维修。乙方可协助甲方安装或维修， 按服务标准收取费用。\n\t\t\t\t\t</p><p>6. 甲方使用电信服务时，应遵守国家法律、法规。\n\t\t\t\t\t</p><p>7. 甲方应对其使用的电信业务终端妥善保管，因甲方使用不当而造成的损失，由甲方自行承担；甲方电信业务终端丟失，应及时办理停机手续，办理停机前产生的损失由甲方承担。\n\t\t\t\t\t</p><p>8. 甲方入网后应立即修改初始产品密码，并注意保管。凡使用甲方产品密码定制、变更电信业务的行为均视为甲方行为，责任由甲方承担。\n\t\t\t\t\t</p><p>三、 乙方的权利与义务\n\t\t\t\t\t</p><p>1. 乙方应积极、努力的为甲方提供优质的电信服务。\n\t\t\t\t\t</p><p>2. 乙方应向甲方提供业务受理、咨询、查询、障碍申告、投诉等服务渠道。\n\t\t\t\t\t</p><p>3. 乙方应在双方约定的期限内为甲方开通其所申请的电信服务业务，若因客观原因无法为甲方开通的，应及时通知甲方。\n\t\t\t\t\t</p><p>4. 乙方按约定的资费标准向甲方收取电信服务费用，并可代收甲方定制的第三方收费业务之费用。\n\t\t\t\t\t</p><p>5. 乙方应保留甲方的电信服务费用信息5个月，以备甲方查询。\n\t\t\t\t\t</p><p>6. 乙方对甲方办理、使用电信业务产生的各类信息资料依法保密；但为建立与甲方沟通渠道，改善服务工作，乙方可以使用本协议涉及的甲方资料。\n\t\t\t\t\t</p><p>7. 乙方保留因技术进步或国家法律法规、政策变动等原因对电信业务的服务功能、操作方法、业务号码等做出调整的权利，但调整时应提前公告并提供相应解决方案。\n\t\t\t\t\t</p><p>8. 乙方在本协议外不得作出对甲方不公平、不合理的规定，或者减轻、免除乙方损害甲方合法权益应当承担的民事责任。\n\t\t\t\t\t</p><p>四、 其他约定\n\t\t\t\t\t</p><p>1.甲方应在乙方自营或授权代理电信业务的经营场所办理电信业务，否则责任自负。\n\t\t\t\t\t</p><p>2.甲方办理各类电信业务的表单均为本协议的补充协议；补充协议与本协议冲突部分 以补充协议为准，补充协议中未约定部分以本协议为准。\n\t\t\t\t\t</p><p>3.乙方提供的标准资费及各类套餐资费有效期为2年，另有约定的以约定为准。期满双方无异议则自动延续，延续期间，一方提出异议即可终止相应资费。\n\t\t\t\t\t</p><p>4.由于不可抗力、政府行为、国家法律法规、规章或政策变动，导致本协议部分或全部无法履行的，双方均不需承担违约责任。\n\t\t\t\t\t</p><p>5.乙方可采用电话、广播、短信、彩信、电子邮件、电视、公开张贴、信函、报纸或 联网等方式进行业务公告、通知。\n\t\t\t\t\t</p><p>6.未经乙方同意并办理过户手续，甲方将协议的全部或部分转让给第三方，对乙方不发生法律效力，责任甶甲方承担。\n\t\t\t\t\t</p><p>五、 违约责任\n\t\t\t\t\t</p><p>1.甲方超过交费时限，每曰按欠费金额的3‰向乙方支付违约金。\n\t\t\t\t\t</p><p>2.乙方提供的电信服务若需终端设备具备相应功能支持，应在办理该业务时告知甲方; 对甲方终端设备不具备相应功能而造成的问题，乙方不承担责任。\n\t\t\t\t\t</p><p>3.甲、乙方中一方违约给对方造成损失，应依法按实际损失向守约方承担赔偿责任。 赔偿责任不包括预期利益、商业信誉损失、数据的丟失、第三方损失以及其他间接损失。\n\t\t\t\t\t</p><p>六、 协议的变更与终止\n\t\t\t\t\t</p><p>1.自甲方办理电信业务注销或过户手续之曰，协议相应解除或转移。\n\t\t\t\t\t</p><p>2.甲方有下列情形之一者，乙方有权暂停或终止向甲方提供本协议约定的部分或全部服务；若引起甲方损失，乙方不承担责任：\n\t\t\t\t\t</p><p>（1）甲方办理入网、变更手续时提供虚假信息资料；\n\t\t\t\t\t</p><p>（2）甲方办理电信业务时有担保法律关系，违反保证条款或担保人无能力屨行保证义务的；\n\t\t\t\t\t</p><p>（3）甲方发送违法信息，或大量发送骚扰信息、拨打骚扰电话等引起接收方投诉或举报并经查实的；\n\t\t\t\t\t</p><p>（4）甲方自行改变电信业务使用性质或超出双方约定使用范围的；\n\t\t\t\t\t</p><p>（5）甲方使用未取得入网许可，或可能影响网络安全或服务质量设备的；\n\t\t\t\t\t</p><p>（6）甲方欠费停机60日仍未补交通信费用和违约金，或因虚假信息资料被停机60日仍未更正信息资料的；\n\t\t\t\t\t</p><p>（7）甲方被司法、行政机关认定为从事违法活动或其他不当用途的；\n\t\t\t\t\t</p><p>（8）其他违反相关法律、法规、规章的行为。\n\t\t\t\t\t</p><p>3.因技术进步、国家政策变动等原因导致本协议无法继续履行的，本协议应终止或变更。\n\t\t\t\t\t</p><p>4.本协议终止后，乙方收回甲方电信服务号码，并保留向甲方追偿所欠费用的权利。\n\t\t\t\t\t</p><p>5.若甲方不符合乙方对本协议项下号卡申请者的审核标准，甲方同意乙方或乙方代理商将甲方的号卡申请转为其他运营商的号卡，按照甲方填写的收件信息邮寄其他运营商的号卡。\n\t\t\t\t\t</p><p>6.甲方收到号卡后可根据其他运营商的协议内容决定是否开通，若甲方将号卡激活则视为同意并接受其他运营商协议，该激活行为与乙方或乙方代理商无关。\n\t\t\t\t\t</p><p>七、 争议解决\n\t\t\t\t\t</p><p>因本协议引起的有关争议，双方协商解决。协商不成，双方均可向消费者协会投泝或电信管理部门申坼；也可向有管辖权的人民法院提起诉讼。\n\t\t\t\t\t</p><p>八、 附则\n\t\t\t\t\t</p><p>本协议自甲方确认阅读后自动生效</p>";break;case 2:this.agreementTitle="个人信息收集协议",this.agreementInfo="<p>尊敬的客户:</p>\n\t\t\t\t\t<p>根据国家法律法规的要求,客户在我公司各类营业网点(含自有营业厅、网上营业厅、授权合作代理商等)办理入网、过户以及需要出示客户证件的有关业务时,客户应配合出示本人有效证件原件并进行登记,登记信息包括客户姓名、证件类型、号码及地址。若客户无法如实提供相关证件,相应的业务无法正常受理。同时,为更好地提供服务,需要客户提供如联系人、联系电话、通信地址、电子邮箱等信息。我公司收集的相关信息将用于保障客户的通信服务安全、了解客户通信需求,为客户提供更加优质的、个性化的产品和服务。\n\t\t\t\t\t</p>\n\t\t\t\t\t<p>我公司高度重视客户个人信息保护工作,采取相关技术和管理手段,对留存客户信息妥善保管,严格保密,不泄露、篡改或者毁损,不出售或者非法向他人提供,不用于提供服务之外的其他目的。</p>\n\t\t\t\t\t<p>我公司收集及使用个人信息遵循如下原则:</p>\n\t\t\t\t\t<p>—、未经客户同意,不得收集、使用客户个人信息。</p>\n\t\t\t\t\t<p>二、不收集提供服务所必需以外的客户个人信息或者将信息使用与其提供服务之外的目的;不以欺骗、误导或者强迫方式或违反法律、行政法规以及双方约定收集、使用信息。</p>\n\t\t\t\t\t<p>三、对在服务提供过程中收集、使用的客户个人信息应严格保密,不得泄露、篡改或者毁损,不得出售或者非法向他人提供。</p>";break;case 3:this.agreementTitle="重要须知",this.agreementInfo='<img class="xy3-con" width="100%" src="https://oss.zjhrnet.com/img/page2/picture/2021090229791620210902161242.jpg"><img class="xy3-con" width="100%" src="https://oss.zjhrnet.com/img/page2/picture/2021090212870820210902161242.jpg">';break;case 4:this.agreementTitle="实名制信息安全责任告知书",this.agreementInfo="<p>尊敬的客户：</p>\n\t\t\t\t\t    <p>感谢您的选择！当您在办理移动号码入网时，请您确保这是您本人的自愿行为，且为您自己使用而办理，不是在他人要求或指使下办理，不是办理后给他人使用。</p>\n\t\t\t\t\t    <p>同时特别提醒您：您应持本人身份证原件办理号码入网，您对本号码拥有使用权。根据国家公安部等五部委《关于依法严厉打击惩戒治理非法买卖电话卡银行卡违法犯罪活动的通告》，入网号码必须实名登记，且不得将号卡租借、贩卖或以任何方式提供给他人。如您的号码被他人利用发生涉恐、诈骗、骚扰等非法违规行为，您将承担本号码项下产生的所有责任，请您确保规范使用您的号码。</p>";break;default:break}this.popupShow=!0},regionConfirm:function(t){this.form.region=t.province.label+"-"+t.city.label+"-"+t.area.label,t.province.label.indexOf("黑龙江")>-1||t.province.label.indexOf("内蒙古")>-1?this.form.provice=t.province.label.substring(0,3):this.form.provice=t.province.label.substring(0,2),this.form.city=t.city.label,this.form.area=t.area.label}}};e.default=n}).call(this,n("543d")["default"])},9631:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return p}));var p={uInput:function(){return Promise.all([n.e("common/vendor"),n.e("uview-ui/components/u-input/u-input")]).then(n.bind(null,"bcf9"))},uCheckboxGroup:function(){return Promise.all([n.e("common/vendor"),n.e("uview-ui/components/u-checkbox-group/u-checkbox-group")]).then(n.bind(null,"8eea"))},uCheckbox:function(){return n.e("uview-ui/components/u-checkbox/u-checkbox").then(n.bind(null,"1ae4"))},uPicker:function(){return Promise.all([n.e("common/vendor"),n.e("uview-ui/components/u-picker/u-picker")]).then(n.bind(null,"87cf"))},uPopup:function(){return n.e("uview-ui/components/u-popup/u-popup").then(n.bind(null,"2987"))},uToast:function(){return n.e("uview-ui/components/u-toast/u-toast").then(n.bind(null,"9309"))}},r=function(){var t=this,e=t.$createElement;t._self._c;t._isMounted||(t.e0=function(e){t.pickerShow=!0})},o=[]}},[["3e41","common/runtime","common/vendor"]]]);