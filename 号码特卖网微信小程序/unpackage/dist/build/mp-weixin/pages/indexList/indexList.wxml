<view class="indexList data-v-09fed2fe"><view class="cityList-sec data-v-09fed2fe"><view class="cityList-search data-v-09fed2fe"><u-search vue-id="38dad8c4-1" placeholder="搜索城市名字" animation="{{true}}" action-style="{{searchButtStyle}}" border-color="#f00" value="{{searchCityName}}" data-event-opts="{{[['^search',[['clickButt']]],['^custom',[['clickButt']]],['^input',[['__set_model',['','searchCityName','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" class="data-v-09fed2fe" bind:__l="__l"></u-search></view><view class="cityList-solt data-v-09fed2fe"><view class="t data-v-09fed2fe">您所在的地区</view><view class="list data-v-09fed2fe"><view data-event-opts="{{[['tap',[['cityClick',['全部']]]]]}}" class="li data-v-09fed2fe" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['cityClick',['$0'],['locationCity']]]]]}}" class="li locationCity data-v-09fed2fe" bindtap="__e">{{''+locationCity+''}}</view></view><view class="t data-v-09fed2fe">全国热门区域</view><view class="list data-v-09fed2fe"><view data-event-opts="{{[['tap',[['cityClick',['北京市']]]]]}}" class="li data-v-09fed2fe" bindtap="__e">北京市</view><view data-event-opts="{{[['tap',[['cityClick',['深圳市']]]]]}}" class="li data-v-09fed2fe" bindtap="__e">深圳市</view><view data-event-opts="{{[['tap',[['cityClick',['杭州市']]]]]}}" class="li data-v-09fed2fe" bindtap="__e">杭州市</view><view data-event-opts="{{[['tap',[['cityClick',['上海市']]]]]}}" class="li data-v-09fed2fe" bindtap="__e">上海市</view></view></view></view><u-index-list vue-id="38dad8c4-2" scrollTop="{{scrollTop}}" index-list="{{indexList}}" offset-top="{{0}}" data-event-opts="{{[['^select',[['citySelect']]]]}}" bind:select="__e" class="data-v-09fed2fe" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-09fed2fe"><u-index-anchor vue-id="{{('38dad8c4-3-'+index)+','+('38dad8c4-2')}}" index="{{item.letter}}" class="data-v-09fed2fe" bind:__l="__l"></u-index-anchor><block wx:for="{{item.data}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['cityClick',['$0'],[[['list','',index],['data','',index1,'regionName']]]]]]]}}" class="list-cell u-border-bottom data-v-09fed2fe" bindtap="__e"><view class="list-cell-text data-v-09fed2fe" data-name="{{item1.regionName}}">{{''+item1.regionName+''}}</view></view></block></view></block></u-index-list><view class="back data-v-09fed2fe"><u-back-top vue-id="38dad8c4-4" scroll-top="{{backScrollTop}}" duration="{{300}}" class="data-v-09fed2fe" bind:__l="__l"></u-back-top></view></view>