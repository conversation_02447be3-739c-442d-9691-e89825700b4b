(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/indexList/indexList"],{"1fd5":function(n,e,t){"use strict";t.d(e,"b",(function(){return c})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){return o}));var o={uSearch:function(){return t.e("uview-ui/components/u-search/u-search").then(t.bind(null,"b43b"))},uIndexList:function(){return t.e("uview-ui/components/u-index-list/u-index-list").then(t.bind(null,"8d43"))},uIndexAnchor:function(){return t.e("uview-ui/components/u-index-anchor/u-index-anchor").then(t.bind(null,"45cb"))},uBackTop:function(){return t.e("uview-ui/components/u-back-top/u-back-top").then(t.bind(null,"1fea"))}},c=function(){var n=this.$createElement;this._self._c},i=[]},2518:function(n,e,t){},2717:function(n,e,t){"use strict";t.r(e);var o=t("c28e"),c=t.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(i);e["default"]=c.a},"363c":function(n,e,t){"use strict";t.r(e);var o=t("1fd5"),c=t("2717");for(var i in c)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return c[n]}))}(i);t("63de");var u=t("f0c5"),l=Object(u["a"])(c["default"],o["b"],o["c"],!1,null,"09fed2fe",null,!1,o["a"],void 0);e["default"]=l.exports},"63de":function(n,e,t){"use strict";var o=t("2518"),c=t.n(o);c.a},6973:function(n,e,t){"use strict";(function(n,e){var o=t("4ea4");t("8b54");o(t("66fd"));var c=o(t("363c"));n.__webpack_require_UNI_MP_PLUGIN__=t,e(c.default)}).call(this,t("bc2e")["default"],t("543d")["createPage"])},c28e:function(n,e,t){"use strict";(function(n){var o=t("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var c=o(t("daa2")),i=o(t("1e08")),u=new i.default({key:"RCBBZ-GJTWW-V5PR5-YLXU6-ZKOM5-Z6FGF"}),l=c.default.list.map((function(n){return n.letter})),r={data:function(){return{scrollTop:0,indexList:l,list:c.default.list,locationCity:"定位中",searchCityName:"",searchButtStyle:{color:"#fff","background-color":"#d83337","border-radius":"10rpx",padding:"10rpx 0"},backScrollTop:0}},onLoad:function(e){var t=this;console.log(e),n.getLocation({type:"wgs84",success:function(n){console.log(n),u.reverseGeocoder({location:"".concat(n.latitude,",").concat(n.longitude),success:function(n){console.log(n),t.locationCity=n.result.address_component.city},fail:function(n){console.error(n)}})},fail:function(n){console.log(n)}})},onUnload:function(){console.log("页面卸载"),n.$off()},onPageScroll:function(n){this.scrollTop=n.scrollTop,this.backScrollTop=n.scrollTop},methods:{clickButt:function(){var e=this,t=n.createSelectorQuery().in(this);t.selectAll(".list-cell-text").boundingClientRect((function(t){for(var o=0;o<t.length;o++)if(t[o].dataset.name.indexOf(e.searchCityName)>-1){n.pageScrollTo({duration:0,scrollTop:t[o].top-50});break}})).exec()},citySelect:function(n){console.log(n)},cityClick:function(e){n.setStorageSync("cityName",e),n.navigateBack({delta:1})}}};e.default=r}).call(this,t("543d")["default"])}},[["6973","common/runtime","common/vendor"]]]);