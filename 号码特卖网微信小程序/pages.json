{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "号码优选网"
			}
		},{
		    "path" : "pages/hotCard/liantong203G/liantong203G",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "中国联通",
		        "enablePullDownRefresh": false
		    }
		    
		},{
		    "path" : "pages/hotCard/jiyun90G/jiyun90G",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "移动19元90G",
		        "enablePullDownRefresh": false
		    }
		    
		},{
			"path": "pages/hotCard/hotCard",
			"style": {
				"navigationBarTitleText": "流量卡",
				"enablePullDownRefresh": false
			}
		
		},{
			"path": "pages/index/numDetail/numDetail",
			"style": {
				"navigationBarTitleText": "下单详情",
				"enablePullDownRefresh": false
			}

		},{
			"path": "pages/center/center",
			"style": {
				"navigationBarTitleText": "个人中心",
				"enablePullDownRefresh": false
			}

		},{
			"path": "pages/center/explain_1/explain_1",
			"style": {
				"navigationBarTitleText": "选号流程",
				"enablePullDownRefresh": false
			}

		},{
			"path": "pages/order/attestation/attestation",
			"style": {
				"navigationBarTitleText": "认证中心",
				"enablePullDownRefresh": false
			}

		},{
			"path": "pages/order/detail/detail",
			"style": {
				"navigationBarTitleText": "订单详情",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/indexList/indexList",
			"style": {
				"navigationBarTitleText": "城市列表"
			}
		},{
			"path": "pages/order/order",
			"style": {
				"navigationBarTitleText": "我的订单",
				"enablePullDownRefresh": false
			}

		}
	    ,{
            "path" : "pages/center/explain_2/explain_2",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "交易条款",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/center/explain_3/explain_3",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "送货政策",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/center/explain_4/explain_4",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "服务承诺",
                "enablePullDownRefresh": false
            }
            
        },{
		    "path" : "pages/index/numDetail/agreement_1/agreement_1",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "入网协议",
		        "enablePullDownRefresh": false
		    }
		    
		},{
            "path" : "pages/index/numDetail/agreement_2/agreement_2",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "收集证明",
                "enablePullDownRefresh": false
            }
            
        }
        
        ,{
            "path" : "pages/hotCard/jiyun30G/jiyun30G",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        },{
		    "path" : "pages/hotCard/guangdian192G/guangdian192G",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "",
		        "enablePullDownRefresh": false
		    }
		    
		},
		{
		    "path" : "pages/index/numDetail/confirmPage/confirmPage",
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "支付",
		        "enablePullDownRefresh": false
		    }
		    
		}
        ,{
            "path" : "pages/hotCard/success/success",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "下单成功",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/center/refund/refund",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "退款申请",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/iconCenter/personalityNum/personalityNum",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "个性专区",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/iconCenter/specialOffer/specialOffer",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "特价专区",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/iconCenter/allNum/allNum",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "全国靓号",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/hotCard/hn29y155G/hn29y155G",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "大流量卡",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/center/problem/problem",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "激活常见问题",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/hotCard/hn29y95g/hn29y95g",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "大流量卡",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/numDetail/agreement_3/agreement_3",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/hotCard/jiyun59y50G/jiyun59y50G",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "大流量卡",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/hotCard/jiyun59y135G/jiyun59y135G",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "大流量卡",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/hotCard/liantong39y220G/liantong39y220G",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "大流量卡",
                "enablePullDownRefresh": false
            }
            
        }
    ],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "号码优选网",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#d83337",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"height": "50px",
		"fontSize": "10px",
		"iconWidth": "20px",
		"spacing": "3px",
		//"iconfontSrc":"static/iconfont.ttf", // app tabbar 字体.ttf文件路径 app 3.4.4+
		"list": [{
			"pagePath": "pages/index/index",
			"iconPath": "static/img/icon/home.png",
			"selectedIconPath": "static/img/icon/home-hover.png",
			"text": "全国靓号"
		}, {
			"pagePath": "pages/hotCard/hotCard",
			"iconPath": "static/img/icon/hot.png",
			"selectedIconPath": "static/img/icon/hot-hover.png",
			"text": "流量卡"
		}, {
			"pagePath": "pages/order/order",
			"iconPath": "static/img/icon/order.png",
			"selectedIconPath": "static/img/icon/order-hover.png",
			"text": "我的订单"
		}, {
			"pagePath": "pages/center/center",
			"iconPath": "static/img/icon/center.png",
			"selectedIconPath": "static/img/icon/center-hover.png",
			"text": "个人中心"
		}]
	},
	"uniIdRouter": {}
}