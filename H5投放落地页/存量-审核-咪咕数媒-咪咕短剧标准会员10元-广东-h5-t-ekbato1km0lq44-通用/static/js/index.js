$(document).ready(function () {
    var phone = '' // 手机号码
    var province = '' // 号码归属地
    var catName = '' // 号码运营商
    var isBh = true // 是否是百合集运
    var isGetCode = false
    var count = 0
    var show = getQueryString('show')
    var time
    let p = getQueryString('p') || ''
    let c = getQueryString('c') || ''
    let clickId = getQueryString('clickid') || getQueryString('qz_gdt') || getQueryString('gdt_vid') || getQueryString('callback') || ''
    let unionSite = getQueryString('union_site') || ''
    let adid = getQueryString('adid') || ''
    let requestid = getQueryString('requestid') || ''
    let orderId = '' //订单编号
    let buisNo = '' //业务流水号
    let cityCode = ''
    let provinceCode = ''
    let smsPhone = ''
    let isAlert = 'y'
    let sendSuccessTime
    let remark
    let showText = ``
    let toUrl = ''
    var token
    let link = location.href
    let a_oId = getQueryString('a_oId') || ''
    let pageid = getQueryString('pageid') || ''
    let wokey = getQueryString('wokey') || ''
    let reqSn = ''
    let telcoOrderNo = ''
    let sendSms_payload = {}

    let upPagePhone = getQueryString('phone') || ''
    let url = location.href
    let url_link = url.split('?')[0]
    let sourcePageId = getQueryString('sourcePageId') || ''
    let pageParamsResult = ''
    var json = {
        "channelCode": "M3GP0003", /**渠道id，每个合作方独有的渠道id必填*/
        "sdkType": "https", /**sdk加载类型，默认https，如注册的域名netid是http则填写http必填*/
        "domain": "https://cardstatic.zjhrnet.com/", /**当前域名，用来匹配netids中的netid必填*/
        "option": { /*option具体说明请看文档*/
            "umark": "Common2",
            "color": ""
        }
    }
    window.MiguSdk.getData(json, function (resultCode, msg, authSessionId) {
        /**二次处理*/
        console.log(resultCode)
        console.log(msg)
        console.log(authSessionId)
        // if (resultCode !== '0000') {
        //     message.info("系统错误,请您稍后再试")
        //     return
        // }
    })
    setTimeout(() => {
        pageParamsResult = pageParams(url_link, sourcePageId)
        pageData(c, phone, `${pageParamsResult.pageId}`, p, 1, `${pageParamsResult.sourcePageId}`)
    }, 300)
    if (upPagePhone) {
        $('#phone').val(upPagePhone)
        let data = JSON.stringify({
            mobile: upPagePhone
        })
        GmAjax('POST', 'commonapi/getNumberCity', 'application/json', data, 'json', function (res) {
            getNumberCityStr = res
            setTimeout(function () {
                pageData(c, upPagePhone, `${pageParamsResult.pageId}`, p, 2, `${pageParamsResult.sourcePageId}`)
            }, 300)
        })
    }

    $('.down_title .t').click(function () {
        $(this).toggleClass('active')
        $('.rule').toggleClass('rule-1-active')
    })


    $('.form-ys-p3').click(function () {
        //console.log($('.rule-title').offset().top);
        // $('html,body').animate({
        // 	scrollTop: $('.rule-title').offset().top
        // }, 500)
        $('.protocol_box .title').html(`隐私协议`)
        $('.protocol_box .policy-content').html(`<h1>个人信息授权与保护声明</h1>
		<p><span class="blod">本页面由浙江弘瑞网络技术有限公司（以下简称为"我们"或"弘瑞网络"）开发。</span> 请您务必仔细阅读并了解本《个人信息授权与保护声明》（以下简称为<span class="blod">"本声明"</span>）。<span class="blod">对于重要条款我们采用粗体及下划线方式进行显著标注以提示您注意。</span></p>
		<p class="blod">您一旦主动在页面填写您的个人信息并进行提交操作，即意味着您同意我们按照本声明收集、使用、共享您的相关信息。若您不接受本声明，请勿登记您的个人信息。</p>
		<p>我们的页面和服务主要面向成人。我们不会主动收集未成年人的个人信息，如果您未满18周岁，请您在监护人陪同下仔细阅读并充分理解本声明，并征得监护人的同意后使用我们的服务或向我们提供信息。
		</p>
		<h2>一、收集信息的原则及目的</h2>
		<p>1. 保障为您所提供的产品或服务功能的正常实现。</p>
		<p>2. 实现对您的推送功能。</p>
		<p>3. 帮助我们维护和改进我们的产品或服务，提升用户体验。</p>
		<h2>二、如何收集和使用您的个人信息</h2>
		<p>我们将合法合规对您的个人信息进行采集、使用。</p>
		<p class="blod">您已知晓并同意在您下次浏览本落地页面时，我们会帮您预先填写您上次输入的历史信息以提升您的使用体验。您亦可拒绝使用预填充功能。</p>
		<h3>（一）收集和使用您的个人信息的范围</h3>
		<p class="blod">1. 下单及订单管理</p>
		<p><span class="blod">
				为了向您配送货物或提供相关服务。当您准备登记您的信息以促成交易的达成时，我们将相应收集您的姓名、手机号、地址、性别和身份证号码（仅特殊行业需要，如运营商、保险）。 </span>
			同时，所涉订单中会载明订单编号、您所购买的商品或服务信息、下单时间等。具体需要填写的信息可能会根据我们提供的产品/服务的不同而有所差异，请以届时向您展示的产品/服务以及所对应的要求填写相关个人信息。 </p>
		<p>2.广告的定向推送</p>
		<p class="blod">
			为实现广告推送功能，在您进入本广告页面并同意本声明时，我们还可能会收集您的产品与/或服务的设备信息，包括网络身份标识信息（手机IPV4地址、IP）、网络信息、手机硬件信息（电量、品牌、型号、内存、屏幕分辨率、CPU信息）和运营商信息、操作日志。
		</p>
		<h3>（二）例外</h3>
		<p>对于《信息安全技术 个人信息安全规范》5.6中规定的情形，我们收集、使用您的个人信息无需事先征得您的授权同意。</p>
		<h2>三、如何共享、转让您的个人信息</h2>
		<h3>（一）共享</h3>
		<p> 仅为实现本声明之目的，我们可能会与<span class="blod">关联公司、合作伙伴</span> 共享您的某些个人信息。 <span class="blod">
				对我们与之共享个人信息的公司、组织和个人，我们会与其签署严格的合规协定或做类似要求，以规范其处理个人信息的行为，并促使其具备个人信息的安全保障能力。</span> 我们将与以下授权合作伙伴共享您的信息∶ </p>
		<p class="blod">
			1.供应商、服务提供商和其他合作伙伴（包括相关代理商）。为向您进行货物配送及提供服务，我们可能会将您主动登记提供的信息向该商品及服务的提供商。以及为此目的所涉及的必要第三方进行共享，并由其在您已经向其授权的范围内使用，包括向您发送货物或与您进行必要的联系。
		</p>
		<p class="blod">
			2.广告、分析服务类的授权合作伙伴。我们可能会将您的匿名化或去标识化的个人信息及您的设备信息与广告、分析服务类的授权合作伙伴共享，以帮助其在不识别您的个人身份的前提下提升广告有效触达率。 </p>
		<h3>（二）转让</h3>
		<p>我们不会将您的个人信息转让给任何第三方，但基于您的明确同意或法律法规要求或涉及合并、收购、分立、重组或破产清算的情况除外。</p>
		<div>
			<h2>四、如何保存与保护您的个人信息我们的信息保护规则∶</h2>
			<p class="blod">
				1.收集到您的个人信息后，在保障供应商、服务提供商和其他合作伙伴或必要第三方能够凭借相关信息进行货物发送或服务提供的基础上，我们将通过技术手段及时对数据进行匿名化或去标识化处理。 </p>
			<p>2.我们已使用符合业界标准的安全防护措施保护您提供的个人信息，防止数据遭到未经授权访问、公开披露等。</p>
			<p><span
					class="blod">3.如果我们的管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改或毁坏，</span>我们将及时将事件相关情况及时向您告知，难以逐一告知时，我们会采取公告的方式。同时，我们还将主动上报个人信息安全事件的处置情况。
			</p>
			<p>我们的信息保存方式∶</p>
			<p>1.保存期限∶您提供的个人信息，将在上文所声明的服务提供过程中持续授权我们使用。在您删除个人信息时，我们将视为您撤回了对于该等个人信息收集、使用或共享的授权和同意。</p>
			<p>2.保存地域∶<span class="blod">原则上，我们所采集到的您的个人信息将存储在中国境内。</span></p>
		</div>
		<h2>五、您对个人信息享有的权利</h2>
		<p>按照中国相关的法律、法规、标准，我们保障您对自己的个人信息的更正权、删除权或授权撤回等相关权益。</p>
		<p class="blod">
			为实现本第五条所述权利，您可通过本声明第七条"如何联系我们"中所列明的联系方式与我们进行联系，我们在验证主体身份，确认相关要求和理由后，为您进行相应的更正、删除及授权撤回。</p>
		<h2>六、政策更新</h2>
		<p> 基于服务优化或情况变更等事由，本声明会不定期进行更新。更新版本将<span
				class="blod">通过在官方网站、广告落地页面等我们正在运营或进行合作的各类相关产品中发出，也请您访问我们网站及软件以便及时了解最新的《个人信息授权与保护声明》。</span></p>
		<h2>七、联系我们</h2>
		<p>您可通过广告落地页中的客服电话联系我们，我们将会在15个工作日内给您反馈。</p>
		<h2>八、争议解决</h2>
		<p class="blod">本声明的涉诉争议由杭州市滨江区人民法院管辖。</p>`)
        $('.bg').show()
        $('.protocol_box').show()
    })
    $('.xuzhi .text').click(function () {
        $('.protocol_box .title').html('订购须知')
        $('.protocol_box .policy-content').html(`<p data-v-84738e7e="">1.产品名称：沃音乐畅听流量包10元包月(定制版)</p><p data-v-84738e7e="">2.产品资费：10元/月，订购立即生效，每月月初如不退订自动续费；</p><p data-v-84738e7e="">3.产品内容：订购后用户可享受以下两大权益：</p><p data-v-84738e7e=""> （1）每月可在页面或联通视频彩铃APP免费设置 <span data-v-84738e7e="" class="cont_bd">精选区内的</span> 视频彩铃； </p><p data-v-84738e7e="">（2）用户可激活开通多款APP共享4GB流量；</p><img data-v-84738e7e="" src="https://oss.zjhrnet.com/img/cunliang/liangtong/woyinyue/shipincailing/rule-list.2bab047c.png" alt="" style="width:100%;"><p data-v-84738e7e=""> 4.流量激活：首次使用免流量服务，需先在对应APP进行激活，您可查看 <span data-v-84738e7e="">【激活指引】</span> 查看各APP激活流程，激活成功后次月续订将不需要进行二次激活； </p><p data-v-84738e7e=""> 5.退订或其他问题：如需退订，请点击 <span data-v-84738e7e="">【退订】</span> 进行操作，如果您还有其他问题，请联系客服4000410155（工作时间：10:00-17:30） </p>`)
        $('.bg').show()
        $('.protocol_box').show()
    })

    setTimeout(() => {
        if (show) {
            $('.close-mask').show()
        }
    }, 100)
    // setTimeout(function() {
    // 	if (c != '140f6d95f487480f8d6779d2a2af866a') {
    // 		$('.time-main').show()
    // 		CountDown()
    // 	}
    // }, 20000)
    $('.form-ys-radio').click(function () {
        $(this).toggleClass('form-ys-radio-active');
    })
    $('.form-ys-p2').click(function () {
        console.log($('.rule-title').offset().top);
        $('html,body').animate({
            scrollTop: $('.rule-title').offset().top
        }, 500)
    })
    $('.btn-box').click(function () {
        $('.bg').hide()
        $('.protocol_box').hide()
    })
    $('.cornerMark').click(function () {
        location.href = `${location.origin}/front/complaint/index.html?acid=${c}`
    })
    // $('.confirm-mask').show()
    $('.close-btn1,.confirm-btn1').click(function () {
        //$('.close-mask').hide()
        //$('.close-mask2').show()
    })
    $('.close-btn2').click(function () {
        location.href =
            'https://cloud.gnettechnology.cn/5g_jy_xw/index.html?pid=pg326542&rcd=ch778379&rid=cc355281'
    })
    $('.close-btn').click(function () {
        location.href =
            'https://cloud.gnettechnology.cn/5g_jy_xw/index.html?pid=pg326542&rcd=ch778379&rid=cc355281'
    })
    $("input").blur(function () {
        setTimeout(() => {
            const scrollHeight = document.documentElement.scrollTop || document.body
                .scrollTop || 0;
            window.scrollTo(0, Math.max(scrollHeight - 1, 0));
        }, 100);
    })
    $('.logo').click(function () {
        message.info('中国移动官方渠道，请放心订购')
    })
    var actionCode = function () {
        eval(sucCode) // 执行插码
    }

    $('.form-code').click(function () {
        phone = $('#phone').val()
        if (!phone) {
            message.info('请输入办理的手机号码')
            return
        }
        if (!isPhone(phone)) {
            message.info('请输入正确的手机号码')
            return
        }
        if (count > 0) {
            return
        }
        if (isGetCode) {
            mg_checkPhone()
            return
        }
        let form_ys_radio_active = $('.form-ys-radio').attr('class')
        if (form_ys_radio_active.indexOf('form-ys-radio-active') > -1) {

        } else {
            //message.info('请阅读并勾选协议！')
            showWindwsText('请阅读并勾选协议！')
            return
        }
        pageData(c, phone, `${pageParamsResult.pageId}`, p, 2, `${pageParamsResult.sourcePageId}`)
        mg_checkPhone()
    })

    $('.butt .b').on('click', function () {
        $('.show_windws_text').hide()
        $('.bg').hide()
    })

    function showWindwsText(showtext) {
        $('.show_windws_text .d').html(showtext)
        $('.show_windws_text').show()
        $('.bg').show()
    }

    function showWindwsTextSuccess(url, showtext) {
        $('.show_windws_text .t').html(`您的随心听订单已受理<br>最终以短信通知为准`)
        $('.show_windws_text .d').html(showtext)
        //toUrl = `${url}&c=${c}&clickid=${clickId}`
        toUrl = url
        $('.show_windws_text').show()
        $('.bg').show()
        console.log(toUrl);
        // setTimeout(function() {
        // 	$('.butt .b').click()
        // }, 100000)
    }

    function autoClick() {
        setTimeout(function () {
            if (c == '140f6d95f487480f8d6779d2a2af866a') {

            } else {
                //$('.confirm-btn1').hide()
            }
        }, 5000)
    }

    $('.form-btn').click(function () {
        // $('.form-ys-radio').addClass('form-ys-radio-active');
        phone = $('#phone').val()
        code = $('#code').val()
        if (!phone) {
            message.info('请输入办理的手机号码')
            return
        }
        if (!isPhone(phone)) {
            message.info('请输入正确的手机号码')
            return
        }
        // if (smsPhone == '') {
        // 	message.info('请获取验证码')
        // 	return
        // }
        // if (!code || code.length != 6) {
        // 	message.info('请完整填写验证码')
        // 	return
        // }
        // if ($('#phone').val() != smsPhone) {
        // 	message.info('调皮！请输入获取验证码的手机号')
        // 	return
        // }
        //$('.confirm-mask').show()
        //$('.form-ys-radio').addClass('form-ys-radio-active');
        let form_ys_radio_active = $('.form-ys-radio').attr('class')
        if (form_ys_radio_active.indexOf('form-ys-radio-active') > -1) {

        } else {
            message.info('请阅读并勾选协议！')
            return
        }
        // $('.confirm-mask').show()
        mg_createOrder()
    })
    //获取验证码
    const mg_checkPhone = function () {
        $('.load_bg,.loading').show()
        let data = JSON.stringify({
            pid: p,
            acid: c,
            mobile: phone
        })
        GmAjax('POST', 'clcommonapi/sendSms', 'application/json', data, 'json', function (res) {
            pageData(c, phone, `${pageParamsResult.pageId}`, p, 3, `${pageParamsResult.sourcePageId}`)
            console.log(res);
            if (res.status == 200 || res.status == 201) {
                pageData(c, phone, `${pageParamsResult.pageId}`, p, 4, `${pageParamsResult.sourcePageId}`)
                count = 60
                $('.form-code').text(count)
                isGetCode = true
                time = setInterval(function () {
                    count--
                    $('.form-code').text(count)
                    if (count <= 0) {
                        $('.form-code').text('重新获取')
                        clearInterval(time)
                    }
                }, 1000)
                $('.load_bg,.loading').hide()
                message.info('发送成功')
                smsPhone = $('#phone').val()
                reqSn = res.payload.reqSn
                telcoOrderNo = res.payload.telcoOrderNo
                sendSms_payload = res.payload
            } else {
                pageData(c, phone, `${pageParamsResult.pageId}`, p, 5, `${pageParamsResult.sourcePageId}`)
                $('.load_bg,.loading').hide()
                message.info(res.error)
            }
        })
    }
    //新下单
    const mg_createOrder = function () {
        $('.load_bg,.loading').show()
        let data = JSON.stringify({
            pid: p,
            acid: c,
            mobile: phone,
            clickid: clickId,
            unionSite: unionSite,
            a_oId: a_oId,
            smsCode: $('#code').val(),
            reqSn: reqSn,
            telcoOrderNo: telcoOrderNo,
            remark: sendSms_payload.remark,
        })
        GmAjax('POST', 'clcommonapi/createOrder', 'application/json', data, 'json', function (res) {
            $('.confirm-btn2').removeAttr('style', '')
            $('.confirm-mask').hide()
            $('.load_bg,.loading').hide()
            $('.bg_2').hide()
            pageData(c, phone, `${pageParamsResult.pageId}`, p, 6, `${pageParamsResult.sourcePageId}`)
            if (res.status == 200 || res.status == 201) {
                pageData(c, phone, `${pageParamsResult.pageId}`, p, 7, `${pageParamsResult.sourcePageId}`)
                //$('#phone').val('')
                // setTimeout(function () {
                // 	location.href = "./success.html"
                // }, 500)
                function base64EncodePhone(phoneNumber) {
                    // 创建一个 TextEncoder 对象
                    const encoder = new TextEncoder();
                    // 将手机号字符串转换为 Uint8Array
                    const encoded = encoder.encode(phoneNumber);
                    // 使用 btoa() 函数进行 Base64 编码
                    const base64Encoded = btoa(String.fromCharCode.apply(null, encoded));
                    return base64Encoded;
                }

                const misidn = base64EncodePhone(phone);
                console.log(res.payload.orderId)
                var payjson = {
                    "orderId": res.payload.telCoOrderNO,
                    // "orderId": '0222410112009414399205',
                    "channelCode": "M3GP0003",
                    "productId": "81991193",
                    "misidn": misidn,
                    "actionType": "1",
                };
                window.MiguSdk.migupay(payjson, function (resultCode, msg, data) {
                    console.log(resultCode)
                    console.log(msg)
                    console.log(data)
                    if (resultCode === '0000' || resultCode === '4901') {
                        location.href = `./success.html${location.search}&phone=${phone}`;
                    } else {
                        // message.info(msg)
                        // location.href = `./success.html${location.search}&phone=${phone}`;
                    }
                })

            } else {
                pageData(c, phone, `${pageParamsResult.pageId}`, p, 8, `${pageParamsResult.sourcePageId}`)
                $('.load_bg,.loading').hide()
                $('.bg_2').hide()
                message.info(res.error)
            }
        })
    }

    $('.confirm-btn2').click(function () {
        // $('.confirm-btn2').css({
        // 	'pointer-events': 'none'
        // })
        mg_createOrder()
    })
    $('.confirm-btn1,.confirm-new .close').click(function () {
        $('.confirm-mask').hide()
        // if (acName.indexOf('###') >= 0) {
        // 	if (payload.sysnType == 1) {
        // 		actionCode() //执行插码触发代码
        // 	}
        // 	if (payload.sysnType == 2 || payload.sysnType == 7) {
        // 		touApi(clickId, p, c)
        // 	}
        // }
    })
    $('.error-btn').click(function () {
        $('.error-mask').hide()
    })
    $('.trigger-btn').click(function () {
        actionCode()
    })

    $('#phone').bind("input propertychange", function (e) {
        let lengt = $('#phone').val().length
        console.log(lengt);
        if (lengt >= 1) {
            $('.form_img_1').hide()
            $('.form_img_linghuiyuan').hide()
            if (lengt == 11) {
                if (!isPhone($('#phone').val())) {
                    message.info('请输入正确的手机号码')
                    return
                } else {
                    // $('.form_img_2').show()
                }
            } else {
                $('.form_img_2').hide()
            }
        } else {
            //$('.form_img_1').show()
        }
    })
    $('#code').bind("input propertychange", function (e) {
        let lengt = $('#code').val().length
        if (c == '140f6d95f487480f8d6779d2a2af866a') {
            $('.form_img_3').hide()
            $('.form_img_4').show()
        } else {
            if (lengt == 6) {
                if (smsPhone) {
                    if (isAlert == 'n') {
                        //$('.bg_2').show()
                        //mg_createOrder()
                    } else {
                        // $('.confirm-mask').show()
                        $('.form_img_3').hide()
                        $('.form_img_4').show()
                    }
                }
            }
        }
    })
});
