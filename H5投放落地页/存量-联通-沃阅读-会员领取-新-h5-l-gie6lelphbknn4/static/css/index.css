* {
  margin: 0;
  padding: 0;
}
body {
  background: #fff;
}
.main {
  max-width: 7.5rem;
  margin: 0 auto;
}
.main .backMark {
  position: absolute;
  top: 0.2rem;
  left: 0;
  z-index: 9;
}
.main .backMark a {
  background-color: rgba(0, 0, 0, 0.5);
  display: block;
  width: 1.1rem;
  height: 0.48rem;
  border-radius: 0rem 0.2rem 0.2rem 0rem;
  opacity: 0.8;
  text-decoration: none;
  text-align: center;
  line-height: 0.48rem;
  color: #fff;
  font-size: 0.24rem;
}
.main .top {
  background: url(https://oss.zjhrnet.com/img/cunliang/jiyun/suixinkan/huangse/receive/T1.jpg) no-repeat;
  background-position: center;
  background-size: contain;
  width: 7.5rem;
  height: 13.34rem;
  margin: 0 auto;
}
.main .top_banner {
  width: 7.5rem;
}

.main .top_number {
  font-size: 0.3rem;
  position: absolute;
  top: 3.1rem;
  left: 1.8rem;
}

.main .top .section {
  width: 5.5rem;
  margin: 0 auto 0;
  padding-top: 2.5rem;
}
.main .top .section .title {
  font-size: 0.32rem;
  color: #1852b6;
  font-weight: bold;
  text-align: center;
}
.main .top .section .title span {
  font-size: 0.26rem;
}
.main .top .section .logo_list {
  margin-top: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  height: 3.5rem;
}
.main .top .section .logo_list .li {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.8rem;
  height: 1.8rem;
  border: 0.01rem solid transparent;
  box-sizing: border-box;
}
.main .top .section .logo_list .li img {
  width: 1.3rem;
}
.main .top .section .logo_list .active {
  border: 0.01rem solid #1852b6;
  border-radius: 0.2rem;
  position: relative;
}
.main .top .section .logo_list .active::after {
  position: absolute;
  top: 0.1rem;
  right: 0.1rem;
  content: '';
  width: 0.25rem;
  height: 0.25rem;
  background: url('https://oss.zjhrnet.com/img/cunliang/jiyun/suixinting/danlanse/toutiao/receive/ok.png') no-repeat;
  background-position: center;
  background-size: contain;
}
.main .top .tips {
  font-size: 0.2rem;
  width: 5.5rem;
  margin: 1.5rem auto 0;
}
.main .banner {
  width: 7.5rem;
  height: 3.19rem;
  background: url('https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/T1.jpg') no-repeat;
  background-position: center;
  background-size: contain;
}
.main .section {
  margin-top: -1.7rem;
  padding: 0 0.2rem;
}
.main .section .pro_list {
  min-height: 3.9rem;
}
.main .section .pro_list .title {
  display: flex;
  align-items: center;
  font-size: 0.4rem;
  font-weight: bold;
  margin-top: 0.4rem;
  display: none;
}
.main .section .pro_list .line {
  display: inline-block;
  width: 0.05rem;
  height: .3rem;
  background: #62abf0;
}
.main .section .pro_list .title .text {
  font-size: 0.33rem;
  padding-left: 0.2rem;
}
.main .section .pro_list .list {
  width: 100%;
  display: flex;
  border-radius: 0.3rem !important;
  justify-content: center;
  margin-top: 0.2rem;
  flex-wrap: wrap;
  gap: 0.2rem;
  display: none;
  padding-top: 1rem;
  background: rgba(182, 223, 253, 0.15);
}
.main .section .pro_list .list .li {
  width: 1.45rem;
  height: 2.3rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  border: 0.01rem #ccc dashed;
  padding: 0.1rem 0.1rem;
  /* margin: 0 auto; */
  box-sizing: border-box;
  border-radius: .2rem;
  box-shadow: 0 0.05rem 0.1rem #ccc;
  margin-bottom: 0.3rem;
  overflow: hidden;
  background: #fff;
}
.main .section .pro_list .list .li img {
  width: .9rem;
  height: .9rem; /* 添加固定高度以确保正方形 */
  border-radius: 50%; /* 设置为圆形 */
  object-fit: cover; /* 保持图片比例并填充容器 */
}
.main .section .pro_list .list .li .text {
  height: .7rem;
  font-size: 0.22rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 0.35rem; 
}
.main .section .pro_list .list .li .immediate {
  width: 100%;
  color: #fff;
  padding: 0.05rem 0;
  border-radius: 0.2rem;
  font-size: 0.2rem;
  background: #FFAD4D;
}
.main .section .pro_list .list .active {
  border: 0.01rem solid #ff0000;
  border-radius: 0.2rem;
  position: relative;
}
.main .section .pro_list .list .active::after {
  position: absolute;
  bottom: -0.15rem;
  right: -0.15rem;
  content: '';
  width: 0.49rem;
  height: 0.47rem;
  background: url('https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/dagou.png') no-repeat;
  background-position: center;
  background-size: contain;
}
.main .section .pro_list .no {
  text-align: center;
  font-size: 0.4rem;
  color: #ccc;
  padding: 1.3rem 0 1rem;
  display: none;
}
.main .section .pro_list .no .mydh {
  color: #1852b6;
}
.main .section .butt {
  margin-top: 0.3rem;
  text-align: center;
  background: #00bf55;
  color: #fff;
  font-size: 0.44rem;
  padding: 0.3rem 0;
  display: none;
}
.main .section .tips .title {
  display: flex;
  align-items: center;
  font-size: 0.4rem;
  font-weight: bold;
  margin-top: 0.4rem;
}
.main .section .tips .title .text {
  padding-left: 0.2rem;
}
.main .section .tips .tips_sec {
  padding: 0.2rem 0;
}
.main .section .tips .tips_sec p {
  font-size: 0.3rem;
  color: #3b3b3b;
  line-height: 1.8;
  text-align: justify;
}
.main .bg {
  display: none;
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  top: 0;
  left: 0;
  z-index: 1;
}
.main .loading-mask {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  z-index: 99;
}
.main .loading-mask .loading-border {
  border-radius: 0.2rem;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.main .loading-mask .loading-border .loading {
  width: 0.5rem;
  height: 0.5rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url('../image/2019122759293420191227185225.gif');
  margin: auto;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 10001;
}
.main .loading-mask .loading-border .loading-text {
  color: #fff;
  text-align: center;
  font-size: 0.24rem;
  margin-top: 0.3rem;
}
.main .mask_window {
  display: none;
  position: fixed;
  width: 6.5rem;
  background: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  border-radius: 0.3rem;
}
.main .mask_window .close {
  text-align: right;
  font-size: 0.3rem;
}
.main .mask_window .close img {
  width: 0.31rem;
  margin-right: 0.2rem;
  margin-top: 0.2rem;
}
.main .mask_window .confirm_main {
  display: none;
}
.main .mask_window .confirm_main .title {
  color: #1852b6;
  font-size: 0.36rem;
  text-align: center;
  font-weight: bold;
}
.main .mask_window .confirm_main .img {
  margin-top: 0.2rem;
  text-align: center;
}
.main .mask_window .confirm_main .img img {
  width: 1.5rem;
}
.main .mask_window .confirm_main .butt {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 0.5rem;
}
.main .mask_window .confirm_main .butt .b {
  padding: 0.2rem 0.5rem;
  text-align: center;
  font-size: 0.32rem;
  border-radius: 1rem;
  margin: 0 0.1rem;
}
.main .mask_window .confirm_main .butt .no {
  border: 0.01rem solid #1852b6;
  color: #1852b6;
}
.main .mask_window .confirm_main .butt .yes {
  background: linear-gradient(to right, #ff567b, #fc2162);
  color: #fff;
}
.main .mask_window .code_main {
  display: none;
}
.main .mask_window .code_main .title {
  color: #1852b6;
  font-size: 0.36rem;
  text-align: center;
  font-weight: bold;
}
.main .mask_window .code_main .form-item {
  width: 5.6rem;
  color: #000000;
  margin: auto;
  display: flex;
  overflow: hidden;
  margin-bottom: 0;
  border: none;
  background: none;
  border-radius: 0;
  margin: 0.4rem auto 0.4rem;
}
.main .mask_window .code_main .form-item .form-input {
  font-size: 0.32rem;
  color: #000;
  padding-left: 0.4rem;
  width: 100%;
  outline: none;
  background: #fff;
  border-radius: 0.5rem;
  border: 0.01rem solid #1852b6;
  padding: 0.2rem 0 0.2rem 0.2rem;
}
.main .mask_window .code_main .form-item .form-code {
  flex-shrink: 0;
  color: #fff;
  font-size: 0.3rem;
  background: #fff;
  text-align: center;
  width: 2.2rem;
  margin-left: 0.3rem;
  border-radius: 0.5rem;
  font-weight: bold;
  padding: 0.2rem 0;
  background: #1852b6;
}
.main .mask_window .code_main .form-item .active {
  background: #aed8ff;
  color: #fff;
}
.main .mask_window .code_main .butt {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 0.5rem;
}
.main .mask_window .code_main .butt .b {
  padding: 0.2rem 2rem;
  text-align: center;
  font-size: 0.32rem;
  border-radius: 1rem;
  margin: 0 0.1rem;
}
.main .mask_window .code_main .butt .no {
  border: 0.01rem solid #1852b6;
  color: #1852b6;
}
.main .mask_window .code_main .butt .yes {
  background: linear-gradient(to right, #ff567b, #fc2162);
  color: #fff;
}
.main .mask_window .success_main {
  display: none;
}
.main .mask_window .success_main .title {
  color: #1852b6;
  font-size: 0.36rem;
  text-align: center;
  font-weight: bold;
}
.main .mask_window .success_main .img {
  margin-top: 0.2rem;
  text-align: center;
}
.main .mask_window .success_main .img img {
  width: 2.5rem;
}
.main .mask_window .success_main .tips {
  font-size: 0.26rem;
  color: #1852b6;
  text-align: center;
  font-weight: bold;
  padding-bottom: 0.3rem;
}
.main .mask_window .success_main .butt {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 0.5rem;
}
.main .mask_window .success_main .butt .b {
  padding: 0.2rem 2rem;
  text-align: center;
  font-size: 0.32rem;
  border-radius: 1rem;
  margin: 0 0.1rem;
}
.main .mask_window .success_main .butt .no {
  border: 0.01rem solid #1852b6;
  color: #1852b6;
}
.main .mask_window .success_main .butt .yes {
  background: linear-gradient(to right, #ff567b, #fc2162);
  color: #fff;
}
.out {
  display: none;
  background: url(https://oss.zjhrnet.com/img/cunliang/jiyun/suixinkan/huangse/receive/out.jpg) no-repeat;
  background-position: center;
  background-size: contain;
  width: 7.5rem;
  height: 13.34rem;
  margin: 0 auto;
}
.message {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 0.3rem;
  padding: 0 0.4rem;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.28rem;
  color: #ffffff;
  letter-spacing: 0;
  line-height: 0.36rem;
  z-index: 9999999999999;
  text-align: center;
  padding: 0.2rem;
  display: inline-block;
}
.cornerMark {
  position: absolute;
  top: 0.2rem;
  right: 0;
  z-index: 9;
}
.cornerMark a {
  background-color: rgba(0, 0, 0, 0.5);
  display: block;
  width: 1.45rem;
  height: 0.48rem;
  border-radius: 0.2rem 0 0 0.2rem;
  opacity: 0.8;
  text-decoration: none;
  text-align: center;
  line-height: 0.48rem;
  color: #fff;
  font-size: 0.24rem;
}
.hide {
  pointer-events: none;
  filter: grayscale(1);
}

/* 学生认证弹窗样式 */
.student-auth-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); */
    z-index: 1001;
    /* width: 80%; */
    /* max-width: 350px; */
    width: 6.91rem;
    height: 8.08rem;
    display: none;
    /* background: url(https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/iconImg/other/mask.png)no-repeat; */
    background: url(https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/iconImg/other/mask2.png)no-repeat;
    background-position: center;
    background-size: contain;
}

.popup-body {
  position: absolute;
  bottom: .65rem;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
  text-align: center;
}

.popup-title{
  font-size: 0.23rem;
  position: absolute;
  top: 3.3rem;
  left: 50%;
  width: 6.2rem;
  transform: translateX(-50%);
  text-align: center;
}

.close-btn {
    display: inline-block;
    text-decoration: none;
    font-size: 16px;
    transition: background 0.3s;
    background: url(https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/iconImg/other/left.png) no-repeat;
    background-position: center;
    background-size: contain;
    width: 2.72rem;
    height: .78rem;
}

.auth-btn {
    display: inline-block;
    text-decoration: none;
    font-size: 16px;
    transition: background 0.3s;
    background: url(https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/iconImg/other/right.png) no-repeat;
    background-position: center;
    background-size: contain;
    width: 2.72rem;
    height: .78rem;
}

.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.li.disabled {
  pointer-events: none;
  filter: grayscale(100%);
}

.disabled-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  filter: grayscale(100%);
}
.popup-tips-container {
  display: none;
}

/* 领取成功弹窗样式 */
.popup-tips {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    width: 5.5rem;
    height: 1.8rem;
    font-size: 0.33rem;
    text-align: left;
    background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
    border-radius: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1852b6;
    font-weight: 600;
    border: 1px solid rgba(24, 82, 182, 0.1);
    backdrop-filter: blur(5px);
    animation: popupFadeIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    padding: 0 0.4rem;
    letter-spacing: 0.01rem;
}

@keyframes popupFadeIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    70% {
        transform: translate(-50%, -50%) scale(1.03);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}
.popup-btn-close {
  position: absolute;
  top: -.35rem;
  right: -.39rem;
  background-position: center;
  background-size: contain;
  background-image: url(https://oss.zjhrnet.com/img/cunliang/liangtong/0321/hudong/close.png);
  width: .5rem;
  height: .5rem;
}
/*# sourceMappingURL=index.css.map */