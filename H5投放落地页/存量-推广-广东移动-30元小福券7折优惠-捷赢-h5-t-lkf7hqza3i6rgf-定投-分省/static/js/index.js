$(async function () {
    let phone = ''
    let count = 0;
    let isGetCode = false;
    let smsPhone = "";
    let reqSn = "";
    let telcoOrderNo = "";
    let sendSms_payload;
    let p = getQueryString("p") || "";
    let c = getQueryString("c") || "";
    let code = ''
    let a_oId = getQueryString('a_oId') || ''
    let clickId =
        getQueryString("clickid") ||
        getQueryString("qz_gdt") ||
        getQueryString("gdt_vid") ||
        getQueryString("callback") ||
        "";
    let unionSite = getQueryString("union_site") || "";
    let userAgent = navigator.userAgent || "";
    let pageParamsResult = "";
    let url = location.href;
    // let url = 'https://cardstatic.zjhrnet.com/front/h5-t-hwxlvk6tcyyiiy/index.html?p=2c98ef5f8c3e037c018c6b29ba0300b2&c=117604a5c0b245f09f18b01b8e026d9a';
    let url_link = url.split("?")[0];
    let sourcePageId = getQueryString("sourcePageId") || "";
    let packageName = "";
    let regex = /([a-zA-Z_][a-zA-Z0-9_]*)+([.][a-zA-Z_][a-zA-Z0-9_]*)+/;
    let match = userAgent.match(regex);
    console.log(match)
    if (match && match[0]) {
        packageName = match[0]
    } else {
        console.log("No match found");
    }
    //
    // const script = document.createElement('script')
    // script.src = 'https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js'
    // script.onload = () => {
    //     console.log('crypto-js.js 已加载完成')
    // }
    // document.head.appendChild(script)
    //

    sessionStorage.setItem('gdydpId', p)
    sessionStorage.setItem('gdydHost', "h1.hzjieyingnet.cn")

    setTimeout(function () {
        pageParamsResult = pageParams(url_link, sourcePageId);
        pageData(
            c,
            phone,
            `${pageParamsResult.pageId}`,
            p,
            1,
            `${pageParamsResult.sourcePageId}`,
            userAgent,
            packageName
        );
    }, 300);

    const getAd = function (ad_position, error_position) {
        $('.provice-fixed').show()
        Ad_Show(`${phone}`, `${getNumberCityStr.payload.newObj.p}`, `${getNumberCityStr.payload.newObj.c}`, `${ad_position}`, '无法办理该业务', '融合产品', 'FCS', c, p, error_position, `${pageParamsResult.pageId}`, `${pageParamsResult.sourcePageId}`, packageName)
        // $('.provice-fixed').hide()
    }

    gdp('init', '8d2279a5e2f18b7c', '9d59ac736f01c688', {
        host: 'collect.gmcc.net',
        compress: false,
        hashtag: true,
        debug: true
    });
    gdp('setGeneralProps', {
        "global_merchantsId_var": "GDOPEN_2130016",// 商户id
        "global_merchants_var": "杭州捷赢技术有限公司",// 商户名称
        "global_apiId_var": "smscodechkodcommitorder",// 接口id；传产品受理的接口ID
        "global_apiName_var": "短信验证及订单提交",// 调用接口名称；传产品受理的接口名称
        "global_merchantsIdCity_var": "省统"//商户ID归属地市，枚举值：省统、广州、中山、云浮……
    });
    // gdp('setUserAttributes', {
    //     "operators_var": "移动", // 赋值运营商名称（移动、联通、电信）。
    //     "prov_var": autoLinkInfo.province, // 赋值省份名称。
    //     "city_var": autoLinkInfo.city // 赋值地市名称。
    // });


    event_upload("页面浏览", apiId = null, apiName = null, aesPhone = null, null, null, null, pId = p);


    // 页面url带号码
    let upPagePhone = getQueryString('phone') || ''
    if (upPagePhone) {
        $('#phone').val(upPagePhone)
        phone = $("#phone").val()
        console.log(phone)

        event_upload("输入电话号码", apiId = null, apiName = null, aesPhone = encryptAES(phone), null, null, null, pId = p);

        setTimeout(function () {
            getNumberCity(phone)
        }, 700)
    }
    // 隐私协议
    const POLICY_CONTENT = `<h1>个人信息授权与保护声明</h1>
				<p><span class="bold">本页面由浙江弘瑞网络技术有限公司（以下简称为"我们"或"弘瑞网络"）开发。</span> 请您务必仔细阅读并了解本《个人信息授权与保护声明》（以下简称为<span class="bold">"本声明"</span>）。<span class="bold">对于重要条款我们采用粗体及下划线方式进行显著标注以提示您注意。</span></p>
				<p class="bold">您一旦主动在页面填写您的个人信息并进行提交操作，即意味着您同意我们按照本声明收集、使用、共享您的相关信息。若您不接受本声明，请勿登记您的个人信息。</p>
				<p>我们的页面和服务主要面向成人。我们不会主动收集未成年人的个人信息，如果您未满18周岁，请您在监护人陪同下仔细阅读并充分理解本声明，并征得监护人的同意后使用我们的服务或向我们提供信息。
				</p>
				<h2>一、收集信息的原则及目的</h2>
				<p>1. 保障为您所提供的产品或服务功能的正常实现。</p>
				<p>2. 实现对您的推送功能。</p>
				<p>3. 帮助我们维护和改进我们的产品或服务，提升用户体验。</p>
				<h2>二、如何收集和使用您的个人信息</h2>
				<p>我们将合法合规对您的个人信息进行采集、使用。</p>
				<p class="bold">您已知晓并同意在您下次浏览本落地页面时，我们会帮您预先填写您上次输入的历史信息以提升您的使用体验。您亦可拒绝使用预填充功能。</p>
				<h3>（一）收集和使用您的个人信息的范围</h3>
				<p class="bold">1. 下单及订单管理</p>
				<p><span class="bold">
						为了向您配送货物或提供相关服务。当您准备登记您的信息以促成交易的达成时，我们将相应收集您的姓名、手机号、地址、性别和身份证号码（仅特殊行业需要，如运营商、保险）。 </span>
					同时，所涉订单中会载明订单编号、您所购买的商品或服务信息、下单时间等。具体需要填写的信息可能会根据我们提供的产品/服务的不同而有所差异，请以届时向您展示的产品/服务以及所对应的要求填写相关个人信息。 </p>
				<p>2.广告的定向推送</p>
				<p class="bold">
					为实现广告推送功能，在您进入本广告页面并同意本声明时，我们还可能会收集您的产品与/或服务的设备信息，包括网络身份标识信息（手机IPV4地址、IP）、网络信息、手机硬件信息（电量、品牌、型号、内存、屏幕分辨率、CPU信息）和运营商信息、操作日志。
				</p>
				<h3>（二）例外</h3>
				<p>对于《信息安全技术 个人信息安全规范》5.6中规定的情形，我们收集、使用您的个人信息无需事先征得您的授权同意。</p>
				<h2>三、如何共享、转让您的个人信息</h2>
				<h3>（一）共享</h3>
				<p> 仅为实现本声明之目的，我们可能会与<span class="bold">关联公司、合作伙伴</span> 共享您的某些个人信息。 <span class="bold">
						对我们与之共享个人信息的公司、组织和个人，我们会与其签署严格的合规协定或做类似要求，以规范其处理个人信息的行为，并促使其具备个人信息的安全保障能力。</span> 我们将与以下授权合作伙伴共享您的信息∶ </p>
				<p class="bold">
					1.供应商、服务提供商和其他合作伙伴（包括相关代理商）。为向您进行货物配送及提供服务，我们可能会将您主动登记提供的信息向该商品及服务的提供商。以及为此目的所涉及的必要第三方进行共享，并由其在您已经向其授权的范围内使用，包括向您发送货物或与您进行必要的联系。
				</p>
				<p class="bold">
					2.广告、分析服务类的授权合作伙伴。我们可能会将您的匿名化或去标识化的个人信息及您的设备信息与广告、分析服务类的授权合作伙伴共享，以帮助其在不识别您的个人身份的前提下提升广告有效触达率。 </p>
				<h3>（二）转让</h3>
				<p>我们不会将您的个人信息转让给任何第三方，但基于您的明确同意或法律法规要求或涉及合并、收购、分立、重组或破产清算的情况除外。</p>
				<div>
					<h2>四、如何保存与保护您的个人信息我们的信息保护规则∶</h2>
					<p class="bold">
						1.收集到您的个人信息后，在保障供应商、服务提供商和其他合作伙伴或必要第三方能够凭借相关信息进行货物发送或服务提供的基础上，我们将通过技术手段及时对数据进行匿名化或去标识化处理。 </p>
					<p>2.我们已使用符合业界标准的安全防护措施保护您提供的个人信息，防止数据遭到未经授权访问、公开披露等。</p>
					<p><span
							class="bold">3.如果我们的管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改或毁坏，</span>我们将及时将事件相关情况及时向您告知，难以逐一告知时，我们会采取公告的方式。同时，我们还将主动上报个人信息安全事件的处置情况。
					</p>
					<p>我们的信息保存方式∶</p>
					<p>1.保存期限∶您提供的个人信息，将在上文所声明的服务提供过程中持续授权我们使用。在您删除个人信息时，我们将视为您撤回了对于该等个人信息收集、使用或共享的授权和同意。</p>
					<p>2.保存地域∶<span class="bold">原则上，我们所采集到的您的个人信息将存储在中国境内。</span></p>
				</div>
				<h2>五、您对个人信息享有的权利</h2>
				<p>按照中国相关的法律、法规、标准，我们保障您对自己的个人信息的更正权、删除权或授权撤回等相关权益。</p>
				<p class="bold">
					为实现本第五条所述权利，您可通过本声明第七条"如何联系我们"中所列明的联系方式与我们进行联系，我们在验证主体身份，确认相关要求和理由后，为您进行相应的更正、删除及授权撤回。</p>
				<h2>六、政策更新</h2>
				<p> 基于服务优化或情况变更等事由，本声明会不定期进行更新。更新版本将<span
						class="bold">通过在官方网站、广告落地页面等我们正在运营或进行合作的各类相关产品中发出，也请您访问我们网站及软件以便及时了解最新的《个人信息授权与保护声明》。</span></p>
				<h2>七、联系我们</h2>
				<p>您可通过广告落地页中的客服电话联系我们，我们将会在15个工作日内给您反馈。</p>
				<h2>八、争议解决</h2>
				<p class="bold">本声明的涉诉争议由杭州市滨江区人民法院管辖。</p>`
    $(".protocol_box .policy-content").html(`
    ${POLICY_CONTENT}
    `)

    $('.form-btn').css({
        animation: 'move 0.7s infinite',
    })
    //内部流转
    const internalCirculation = async function (data) {
        return new Promise((resolve, reject) => {
            GmAjax('POST', 'clcommonapi/determineFusionQuota', 'application/json', data, 'json', function (res) {
                resolve(res);
            })
        })
    }
    //推荐接口
    const recommend = async function (data) {
        return new Promise((resolve, reject) => {
            GmAjax_get('GET', 'clcommonapi/getPhoneProductList', 'application/json', data, 'json', function (res) {
                resolve(res);
            })
        })
    }

    var getNumberCityStr = ''
    const getNumberCity = function (phone) {
        $(".load_bg").show();
        // $('.provice-fixed').show()
        let data = JSON.stringify({
            mobile: phone
        })
        pageData(c, phone, `${pageParamsResult.pageId}`, p, 2, `${pageParamsResult.sourcePageId}`)
        GmAjax('POST', 'commonapi/getNumberCity', 'application/json', data, 'json', async function (res) {
            getNumberCityStr = res
            console.log(getNumberCityStr)
            if (getNumberCityStr.payload.newObj.cmcc !== '移动' || getNumberCityStr.payload.newObj.p !== '广东') {
                navigateToNextPage(phone)
                $(".load_bg").hide();
                return
            }
            mg_checkPhone()
        })
    }

    const navigateToNextPage = function (phone) {
        $('.provice-fixed').show()
        setTimeout(() => {
            if (sourcePageId) {
                location.href = `https://cardstatic.zjhrnet.com/front/autopage/index.html?page=a64fda888bb240b5aeacd196bca6e77e&p=2c98ef5f8c3e037c018c6b29ba0300b2&c=${c}&phone=${phone}&clickid=${clickId}&a_oId=${a_oId}`
            } else {
                location.href = `https://cardstatic.zjhrnet.com/front/autopage/index.html?page=a64fda888bb240b5aeacd196bca6e77e&p=2c98ef5f8c3e037c018c6b29ba0300b2&c=${c}&phone=${phone}&sourcePageId=${pageParamsResult.sourcePageId}&clickid=${clickId}&a_oId=${a_oId}`
            }
        }, 1500)
        // $('.provice-fixed').hide()
    }

    //判定手机号码输入完整
    $("#phone").bind("input propertychange", function (e) {
        let lengt = $("#phone").val().length;
        phone = $("#phone").val();
        if (lengt >= 1) {
            $(".form_img_1").hide();
            if (lengt == 11) {
                if (!isPhone($("#phone").val())) {
                    message.info("请输入正确的手机号码");
                    return;
                } else {
                    console.log("正确，跳转");
                    getNumberCity(phone)
                    event_upload("输入电话号码", apiId = null, apiName = null, aesPhone = encryptAES(phone), null, null, null, pId = p);
                }
            } else {
                $(".form_img_1").show();
                $(".form_img_linghuiyuan").show();
                $(".form_img_2").hide();
            }
        } else {
            $(".form_img_1").show();
            $(".form_img_linghuiyuan").show();
            $(".form_img_2").hide();
            $(".form_img_3").hide();
        }
    });
    //--//
    //弹窗提示--//
    function showWindwsText(showtext) {
        // $(".show_windws_text .d").html(showtext);
        // $(".show_windws_text").show();
        // $(".bg").show();
        message.info(`${showtext}`)
    }

    $(".show_windws_text .butt .b").on("click", function () {
        $(".show_windws_text").hide();
        $(".bg").hide();
    });

    $('.form-ys-radio').click(function () {
        $(this).toggleClass('form-ys-radio-active');
    })
    //--//
    //获取验证码
    $(".form-code").click(function () {
        $(".form-ys-radio").addClass("form-ys-radio-active");
        phone = $("#phone").val();
        if (!phone) {
            showWindwsText("请输入办理的手机号码");
            return;
        }
        if (!isPhone(phone)) {
            showWindwsText("请输入正确的手机号码");
            return;
        }
        if (count > 0) {
            return;
        }
        if (isGetCode) {
            // mg_checkPhone();
            return;
        }
        let form_ys_radio_active = $(".form-ys-radio").attr("class");
        if (form_ys_radio_active.indexOf("form-ys-radio-active") > -1) {
        } else {
            message.info("请阅读并勾选协议！");
            return;
        }
        mg_checkPhone();
    });
    //获取验证码
    const mg_checkPhone = function () {
        $(".load_bg").show();
        let data = JSON.stringify({
            pid: p,
            acid: c,
            mobile: phone,
            entranName: packageName
        });
        pageData(
            c,
            phone,
            `${pageParamsResult.pageId}`,
            p,
            3,
            `${pageParamsResult.sourcePageId}`
        );
        event_upload("获取短信验证码", "smscodeapply", apiName = "短信验证码申请", aesPhone = encryptAES(phone), null, null, null, pid = p);
        GmAjax(
            "POST",
            "clcommonapi/sendSms",
            "application/json",
            data,
            "json",
            function (res) {
                console.log(res);
                if (res.status == 200 || res.status == 201) {
                    pageData(
                        c,
                        phone,
                        `${pageParamsResult.pageId}`,
                        p,
                        4,
                        `${pageParamsResult.sourcePageId}`
                    );
                    pageData(
                        c,
                        phone,
                        `${pageParamsResult.pageId}`,
                        p,
                        11,
                        `${pageParamsResult.sourcePageId}`,
                        2
                    );
                    count = 60;
                    $(".form-code").text(count);
                    isGetCode = true;
                    time = setInterval(function () {
                        count--;
                        $(".form-code").text(count);
                        if (count <= 0) {
                            $(".form-code").text("重新获取");
                            clearInterval(time);
                            isGetCode = false;
                        }
                    }, 1000);
                    smsPhone = $("#phone").val();
                    reqSn = res.payload.reqSn;
                    telcoOrderNo = res.payload.telcoOrderNo;
                    sendSms_payload = res.payload;
                    $(".form_img_3").show();
                    $(".form_img_2").hide();
                    $(".loading").hide();
                    $(".load_bg").hide()
                    message.info("发送成功");
                } else {
                    $(".load_bg,.loading").hide();
                    pageData(
                        c,
                        phone,
                        `${pageParamsResult.pageId}`,
                        p,
                        5,
                        `${pageParamsResult.sourcePageId}`
                    );
                    message.info(`${res.error}`);
                    getAd('send_error', '1')
                    // navigateToNextPage(phone)
                }
            }
        );
    };
    //下单按钮
    $(".form-btn,.click-btn").click(function () {
        $('.form-ys-radio').addClass('form-ys-radio-active');
        phone = $("#phone").val();
        code = $("#code").val();
        if (!phone) {
            showWindwsText("请输入办理的手机号码");
            return;
        }
        if (!isPhone(phone)) {
            showWindwsText("请输入正确的手机号码");
            return;
        }
        if (smsPhone == "") {
            showWindwsText("请获取验证码");
            return;
        }
        if (!code) {
            showWindwsText("请完整填写验证码");
            return;
        }
        // 判断验证码是否为纯数字，不含空格和中文或字符
        if (!/^[0-9]+$/.test(code)) {
            showWindwsText("请输入正确的验证码");
            return;
        }
        if ($("#phone").val() != smsPhone) {
            message.info("调皮！请输入获取验证码的手机号");
            return;
        }
        let form_ys_radio_active = $(".form-ys-radio").attr("class");
        if (form_ys_radio_active.indexOf("form-ys-radio-active") > -1) {
        } else {
            showWindwsText("请阅读并勾选协议！");
            return;
        }
        // mg_createOrder()
        $(".confirm-mask").show()
    });


    //下单
    const mg_createOrder = function () {
        $(".load_bg").show();
        let data = JSON.stringify({
            pid: p,
            acid: c,
            mobile: phone,
            a_oId: a_oId,
            clickid: clickId,
            unionSite: unionSite,
            smsCode: $("#code").val(),
            reqSn: sendSms_payload.reqSn,
            telcoOrderNo: sendSms_payload.telcoOrderNo,
            remark: sendSms_payload.remark,
            other: sendSms_payload.other,
            pageUrl: 'https://h1.hzjieyingnet.cn/front/autopage/index.html?page=ec0f87057465495aaaf79a1ef672eb6a&p=2c98ef5f97d4da110197e2a3b6f5002b&c=ed0652a6ad1249fbbd1103d1d396f905'
        });
        pageData(
            c,
            phone,
            `${pageParamsResult.pageId}`,
            p,
            6,
            `${pageParamsResult.sourcePageId}`
        );
        //return
        GmAjax(
            "POST",
            "clcommonapi/createOrder",
            "application/json",
            data,
            "json",
            function (res) {
                if (res.status == 200 || res.status == 201) {
                    pageData(
                        c,
                        phone,
                        `${pageParamsResult.pageId}`,
                        p,
                        7,
                        `${pageParamsResult.sourcePageId}`
                    );
                    const orderId = sessionStorage.getItem('currentOrderId'); // 获取之前保存的订单号
                    event_upload("办理成功", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(phone), orderId, res.payload.telCoOrderNO, null, pid = p);

                    $("#phone").val("");
                    $("#code").val("");
                    setTimeout(function () {
                        $(".load_bg,.loading").hide();
                        $(".bg_2").hide();
                        location.href = `../page-public/mode_page_refresh_success/success.html${location.search}&pid=${p}&acid=${c}&phone=${phone}&city=${getNumberCityStr.payload.newObj.p}&pageId=${pageParamsResult.pageId}&sourcePageId=${pageParamsResult.sourcePageId}`;
                    }, 500);
                } else {
                    $(".load_bg").hide();
                    pageData(
                        c,
                        phone,
                        `${pageParamsResult.pageId}`,
                        p,
                        8,
                        `${pageParamsResult.sourcePageId}`
                    );
                    const errorMessage = res.error
                    const orderId = sessionStorage.getItem('currentOrderId'); // 获取之前保存的订单号
                    event_upload("办理失败", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(phone), orderId, res.payload.telCoOrderNO, errorMessage, pid = p);
                    if (res.error.includes('验证码') || res.error.includes('随机码') || res.error.includes('code check error')) {
                        showWindwsText(`${res.error}`)
                    } else {
                        // navigateToNextPage(phone)
                        getAd('order_error', 2)
                    }
                }
            }
        );
    };

    //弹窗
    $(".confirm-btn2").click(function () {
        $(".confirm-mask").hide()
        const orderId = generateOrderId(12); // 生成订单号
        sessionStorage.setItem('currentOrderId', orderId); // 保存订单号
        event_upload("提交订单", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(phone), orderId, null, null, pid = p);

        mg_createOrder()
    })
    $(".confirm-btn1,.confirm-new .close").click(function () {
        $(".confirm-mask").hide()
    })
    $('#code').bind("input propertychange", function (e) {
        let length = $('#code').val().length
        if (length && smsPhone) {
            $('.form_img_3').hide()
            if ($(".address")) {
                $('.mask-btn').hide()
            }
        } else {
            $('.form_img_3').show()
            if ($(".address")) {
                $('.mask-btn').show()
            }
        }
    })
    //失去焦点
    $("#code").blur(function () {
        let length = $('#code').val().length
        if (isGetCode && length >= 4) {
            pageData(c, phone, `${pageParamsResult.pageId}`, p, 9, `${pageParamsResult.sourcePageId}`)
            event_upload("输入短信验证码", apiId = null, apiName = null, aesPhone = encryptAES(phone), null, null, null, pid = p);
        }
    })
    //活动说明
    $(".form-ys-p3").click(function () {
        $("html,body").animate(
            {
                scrollTop: $(".rule-title").offset().top,
            },
            500
        )
    })
    //隐私政策
    $(".form-ys-p2").click(function () {
        $(".protocol_box .title").html("隐私条款");
        $(".bg").show()
        $(".protocol_box").show()
    })
    $(".btn-box").click(function () {
        $(".bg").hide();
        $(".protocol_box").hide()
    })
})
