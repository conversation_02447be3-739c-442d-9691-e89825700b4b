<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta content="webkit" name="renderer">
    <meta content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover, shrink-to-fit=no"
          name="viewport">
    <meta content="telephone=no,email=no" name="format-detection">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="default" name="apple-mobile-web-app-status-bar-style">
    <meta content="yes" name="full-screen">
    <meta content="true" name="x5-fullscreen">
    <title>联通爱听悦生活酷享会员包</title>
    <meta content="" name="keywords">
    <meta content="" name="description">
    <meta content="pc,mobile" name="applicable-device">
    <link href="../utils/public/css/cunliang/default.css" rel="stylesheet" type="text/css">
    <link href="./static/css/index.css" rel="stylesheet" type="text/css">
    <link href="../utils/favicon_lt.ico" rel="shortcut icon">
    <script src="https://oss.zjhrnet.com/js/utils/jquery-3.3.1.min.js" type="text/javascript"></script>
    <script src="../utils/public/js/init.js" type="text/javascript"></script>
    <script src="../utils/public/js/utils.js" type="text/javascript"></script>
    <script src="../utils/public/js/http.js" type="text/javascript"></script>
    <script src="../utils/public/js/auth_cunl.js" type="text/javascript"></script>
    <script charset="utf-8" src="../utils/all.js" type="text/javascript"></script>
    <script type="text/javascript" src="https://www.gz10010.shop/dlsplugin/plugin.js" async></script>
    <script type="text/javascript">
        window.addEventListener('load', async function(){
            sth_ag.init({
                channelCode: 'GZ-ZJHY',
                productId: '855202205463',
                materialId: '69sbylr56l'
            }).then(res=>{
                console.log(res)
                if(res.resultCode=='0000'){
                    console.log('插件init成功')
                }else{
                    console.log('插件init失败')
                }
            })
        })
    </script>
    
    <!-- <script charset="utf-8" src="../utils/public/js/ad_show.js" type="text/javascript"></script> -->
</head>
<body>
<div class="page">
    <div class="top-img">
        <img src="https://oss.zjhrnet.com/img/cunliang/liangtong/gzlt/sh/20/T1.png">
    </div>
    <div class="form-scroll" style="display: none;">
        <div class="form-scroll-inner">
            <div class="form-scroll-item">155****0736 已领取爱奇艺会员</div>
            <div class="form-scroll-item">186****7032 已领取腾讯视频会员</div>
            <div class="form-scroll-item">131****0830 已领取优酷视频会员</div>
            <div class="form-scroll-item">176****4264 已领取腾讯视频会员</div>
            <div class="form-scroll-item">186****2136 已领取爱奇艺会员</div>
            <div class="form-scroll-item">186****6693 已领取优酷视频会员</div>
            <div class="form-scroll-item">155****1436 抢到了</div>
            <div class="form-scroll-item">186****3520 抢到了</div>
        </div>
    </div>
    <div class="form">
        <img alt="" class="form_img_1" src="https://oss.zjhrnet.com/img/cunliang/liangtong/gif/d-hand1-nj.gif" style="display: none;">
        <img alt="" class="form_img_7" src="https://oss.zjhrnet.com/img/cunliang/sanfang/sh/huiyuan/T3.png">
        <div class="form-item">
            <input class="form-input" id="phone" maxlength="11" placeholder="请输入手机号" type="tel">
        </div>
        <div class="form-item form-item-code"
             style="margin-top: .3rem;border: none;background:none;border-radius:0;">
            <input class="form-input" id="code" maxlength="6" placeholder="请输入短信验证码"
                   style="background: #ffffff;border-radius: .5rem;"
                   type="tel">
            <div class="form-code">获取验证码</div>
        </div>
        <div class="form-btn"></div>
        <div class="form-ys" style="transform: scale(1); font-size: .3rem; margin-top: .1rem;color: #fff;">
            <div class="form-ys-radio"></div>
            <div class="form-ys-p1" style="font-size: .22rem;color: #fff;">我已阅读并同意</div>
            <div class="form-ys-p2" style="font-size: .22rem;">《隐私协议》</div>
            <div class="form-ys-p3" style="font-size: .22rem;color: #fff;">《活动说明》</div>
        </div>
        <div style="color: #fff;text-align: center;font-size: .22rem;margin-top: .15rem;display: none;">
            业务名称：国风国潮视频订阅号 资费20元/月</br>
            注：订购成功立即生效，连续包月，长期有效
        </div>
    </div>
    <div class="pro-img">
        <img src="https://oss.zjhrnet.com/img/cunliang/liangtong/gzlt/sh/20/T3.png">
    </div>
     <div class="rule-title">
        <div class="t" style="text-align: center;font-size: .37rem;color: #fff;">活动说明</div>
    </div>
    <div class="title_img" style="text-align: center;margin-top: .2rem;">
        <img src="https://oss.zjhrnet.com/img/white-down-ddd.png" alt="" style="width: .5rem;">
    </div>
    <div class="tips-text" style="padding: .2rem .3rem;color: #fff;">
        <p>1、产品名称:联通爱听悦生活酷享会员包。
        </p><p>2、产品资费:20元/月。
        </p><p>3、订购范围：除全家福共享版套餐、智慧沃家共享版套餐、达量限速用户及套餐另有规定不可订购流量包的用户外均可订购。
        </p><p>4、产品内容:针对热爱短剧、习惯碎片化阅读的用户精心准备了150本热门短剧资源。涵盖都市虐恋、神医战神甜宠穿越、都市脑洞等类型,随时随时尊享畅听，每月更新资源，长期伴随您的悠闲时光。(含有150部短剧)。
        </p><p>5、会员权益类型:爱奇艺黄金会员、优酷视频会员、腾讯视频会员、芒果TV黄金会员、QQ音乐豪华绿钻、喜马拉雅FM会员、酷我音乐会员、哔哩哔哩大会员、酷狗豪华会员、网易云黑胶、迅雷白金会员、KEEP会员。(权益领取界面有注明【权益内容可能有所调整，具体请以此页面实际显示为准】，您可以选择界面中展示的权益进行领取，感谢您对联通的支持!)(权益领取页面顶部有提示)
        </p><p>6、打开登陆联通爱听APP，点击“我的”-点击“已购买”-“包月”，选择“联通爱听悦生活酷享会员包20元/月-立即生效”即可免费观看包月页面内资源。或关注微信公众号“联通爱听”，点击菜单栏“我的”“我的已购”选择“联通爱听悦生活酷享会员包20元/月-立即生效也可免费观看包月页面内短剧。
        </p><p>7、“AI留声机”是具有首频录制、个性化音色采集、AI变声、TTS导读音色下载分享、设置炫铃振铃等功能的产品。用户订购本业务后，可以免费体验联通爱听平台提供的“AI留声机”功能。即每月可享有AI语音复制功能、个性音库定制、AI智能导读服务。</p>
    </div>
    <img alt="" src="https://oss.zjhrnet.com/img/cunliang/liangtong/gzlt/sh/20/T4.png" style="margin: 0.1rem auto;
		width: 7rem; display: block;">
</div>
<!--背景-->
<div class="bg"></div>
<!--提示信息-->
<div class="show_windws_text">
    <div class="t">
        温馨提示
    </div>
    <div class="d">
        亲爱的用户，您不可办理该产品，<br/>
        请更换联通号码再试试哦~
    </div>
    <div class="butt">
        <div class="b">
            确定
        </div>
    </div>
</div>
<!--加载中-->
<div class="load_bg">
    <div class="loading"></div>
</div>
<!-- 重复确认 -->
<div class="confirm-mask">
    <div class="confirm-new">
        <div class="close"></div>
        <div class="confirm-new-butt">
            <div class="confirm-btn confirm-btn1"></div>
            <div class="confirm-btn confirm-btn2"></div>
        </div>
    </div>
</div>
<!--弹窗条款-->
<div class="protocol_box">
    <div class="title">隐私条款</div>
    <div class="content">
        <div class="policy-content"></div>
    </div>
    <div class="btn-box">确认</div>
</div>
<!--过度-->
<div class="provice-fixed" style="position: fixed;
				top: 0;
				left: 0;
				background: rgba(0, 0, 0, .0);
				z-index: 10001;
				width: 100%;
				height: 100%;
				display: none;">
    <div style="position: absolute;
				top: 50%;
				left: 50%;
				background: rgba(0, 0, 0, .9);
				color: #fff;
				z-index: 99;
				width: 5rem;
				font-size: .38rem;
				transform: translate(-50%,-50%);
				text-align: center;
				box-sizing: border-box;
				padding: .2rem;
				border-radius: .2rem;">
        正在为您匹配优选用户专享活动...
    </div>
</div>
<script src="./static/js/index.js" type="text/javascript"></script>
</body>
</html>
