$(async function () {
    // 广东移动/广西移动插码就绪标志
    window.gdGxInsertCodeReady = false;
    // 项目PV,UV统计,鉴权
    var pid = getQueryString('pid')
    var rcd = getQueryString('rcd')
    var rid = getQueryString('rid')
    var cod = getQueryString('cod')
    var poUid	 // 页面唯一标识
    var codeList = [] // 合作商插码
    var doubleCheck // 是否要二次确认
    var triggerStatus // 是否显示
    var acName
    let page = getQueryString('page') || ''

    let phone = ''
    let count = 0
    let isGetCode = false
    let smsPhone = ""
    let reqSn = ""
    let telcoOrderNo = ""
    let remark = ""
    let sendSms_payload
    let p = getQueryString("p") || ""
    let c = getQueryString("c") || ""
    let code = ''
    let a_oId = getQueryString('a_oId') || ''
    let clickId =
        getQueryString("clickid") ||
        getQueryString("qz_gdt") ||
        getQueryString("gdt_vid") ||
        getQueryString("callback") ||
        ""
    let unionSite = getQueryString("union_site") || ""
    let infoObj = {}
    let url = location.href
    let url_link = url.split("?")[0]
    let sourcePageId = getQueryString("sourcePageId") || ""
    let pageUrlId = ''
    let autoLinkInfo = {}
    let phoneProvince = ''
    let phoneCity = ''
    let productList = []
    var autoInfo = null
    let regex = /([a-zA-Z_][a-zA-Z0-9_]*)+([.][a-zA-Z_][a-zA-Z0-9_]*)+/
    let userAgent = navigator.userAgent || ""
    let match = userAgent.match(regex)
    let packageName = null
    let pos = getQueryString("pos") || ""
    let flag = false
    // 安徽联通js插码字段
    let ahLoginToken = ''
    // 秋末电信js插码字段
    let voucherNumber = ''
    // 广东联通js插码字段
    let gdlt_uuid1 = ''
    let gdlt_uuid2 = ''
    // 贵州联通js插码字段
    let gzlt_uuid1 = ''
    let gzlt_uuid2 = ''
    // 三方支付参数
    let adid = getQueryString('adid') || ''
    let requestid = getQueryString('requestid') || ''
    let pageid = getQueryString('pageid') || ''
    let wokey = getQueryString('wokey') || ''
    let userIp = ''
    let pageUrlName = ''
    let fullUrl = null
    // 三方支付插码字段
    let sanfang_uuid = ''
    let sanfang_orderId = ''
    // 百度信息流新增字段
    let bd_vid = getQueryString('bd_vid') || ''
    // B站信息流新增字段
    let track_id = getQueryString('track_id') || ''

    let data = JSON.stringify({
        pid: p,
        acid: c,
        page
    })
    // 引入咪咕js
    var script_migu = document.createElement('script');
    script_migu.src = 'https://oss.zjhrnet.com/js/utils/migushumei/common.js';
    script_migu.type = 'text/javascript';
    document.head.appendChild(script_migu)

    // 创建加载脚本的Promise函数
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            document.head.appendChild(script);
        });
    }

    const GD_PRODUCT_CONFIG_MAP = {
        "2c98ef5f9565a2a001956f73f7f408de": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=5b767e7604f84a8f8a2241455ea6e52f&p=2c98ef5f9565a2a001956f73f7f408de&c=a30a13d8534e47a89f57cb3a43016907",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f9565a2a001956f83ee7b0903": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=6a1f0028ba784d52a2641a74d41fb77f&p=2c98ef5f9565a2a001956f83ee7b0903&c=e77bf9d595494a9a8904e0badd49562c",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f97682c9701976863a5400015": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=516c66dbcf2642e7adccfeb8c72d5e9e&p=2c98ef5f97682c9701976863a5400015&c=e174bf50910446faa8b501719bfd5803",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f97d4da110197e2a3b6f5002b": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=ec0f87057465495aaaf79a1ef672eb6a&p=2c98ef5f97d4da110197e2a3b6f5002b&c=ed0652a6ad1249fbbd1103d1d396f905",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f97d4da110197e8d6c32d0100": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=a5d3f80e2b6249599cb80e624c255171&p=2c98ef5f97d4da110197e8d6c32d0100&c=043da2d685d24599859be9894a74c159",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f985f67810198635e4d70004d": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=9c78d8a887c74dd689c9d1eab64f8640&p=2c98ef5f985f67810198635e4d70004d&c=eeb581f20dec4c85b66f24cf7128630a",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f98cb75d40198cf64ba0a005c": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=92e9cc1e6ac74dbcbefd9e8291129122&p=2c98ef5f98cb75d40198cf64ba0a005c&c=45d1ae9bf72842269e2538cdc25b57d6",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f98cb75d40198cf67bb1a005e": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=38cbf159316946dcb001dd1c286d816a&p=2c98ef5f98cb75d40198cf67bb1a005e&c=a580e29dbf48428f9e0e83dd1374a695",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f98cb75d40198e4e75e63091a": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=9266cbfef8c84c33881054b2ff5cabe5&p=2c98ef5f98cb75d40198e4e75e63091a&c=c6b5834d30344b46af1860a2c02bdd01",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f98acb11b0198acffeac9000b": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=f29e0573f4b1437394e12d7c35eb77a6&p=2c98ef5f98acb11b0198acffeac9000b&c=acbc45000ca24694bad88a0f57cb7ef6",
            merchantsId: "GDOPEN_2129215",
            merchantsName: "北京易橙天下科技有限公司",
        },
        "2c98ef5f98ee800d0198ef28f469000d": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=7ff8ccfcbe0d41f39c9dc63c3b971d90&p=2c98ef5f98ee800d0198ef28f469000d&c=ee41051ec9464056abf46cbf7a47c144",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f98ee800d0198ef2c4cdf000f": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=75150ebb1a3246ee93199d5c3064c1b8&p=2c98ef5f98ee800d0198ef2c4cdf000f&c=3226be1148844c3b8883a2db02c58d51",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f98ee800d0198ef308a330011": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=ca662cb30bd0484f9893a5c3c77bebda&p=2c98ef5f98ee800d0198ef308a330011&c=b7d6e46c4b834c20929283a7880b308f",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f98f3bf310198f3c6b50b0002": {
            domain: "cardstatic.zjhrnet.com",
            path: "/front/autopage/index.html",
            query: "page=eb21c3b6a73643d09dec55fb08fb2912&p=2c98ef5f98f3bf310198f3c6b50b0002&c=7628c6157f7b478e96d95e0b5325cdd4",
            merchantsId: "GDOPEN_2124413",
            merchantsName: "浙江弘瑞网络技术有限公司",
        },
        "2c98ef5f990e930c0199128cf7610506": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=1ffdd12ddce74e02bbc3632d2fa42754&p=2c98ef5f990e930c0199128cf7610506&c=f32b32a2d78d4bf9aa9abbd3a8c651b2",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f98cb75d40198cf6353bf005b": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=92e9cc1e6ac74dbcbefd9e8291129122&p=2c98ef5f98cb75d40198cf6353bf005b&c=45d1ae9bf72842269e2538cdc25b57d6",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f97d4da110197e2a32f7d002a": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=ec0f87057465495aaaf79a1ef672eb6a&p=2c98ef5f97d4da110197e2a32f7d002a&c=ed0652a6ad1249fbbd1103d1d396f905",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f98cb75d40198cf666db3005d": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=38cbf159316946dcb001dd1c286d816a&p=2c98ef5f98cb75d40198cf666db3005d&c=a580e29dbf48428f9e0e83dd1374a695",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
        "2c98ef5f97682c9701976862f4d70014": {
            domain: "h1.hzjieyingnet.cn",
            path: "/front/autopage/index.html",
            query: "page=516c66dbcf2642e7adccfeb8c72d5e9e&p=2c98ef5f97682c9701976862f4d70014&c=e174bf50910446faa8b501719bfd5803",
            merchantsId: "GDOPEN_2130016",
            merchantsName: "杭州捷赢技术有限公司",
        },
    };

    function getMerchantsInfo(productId) {
        return GD_PRODUCT_CONFIG_MAP[productId] || {
            domain: "",
            merchantsId: "",
            merchantsName: "",
        }
    }

    function getFullUrl(productId) {
        const productConfig = GD_PRODUCT_CONFIG_MAP[productId];
        if (!productConfig) {
            return null;
        }
        return `https://${productConfig.domain}${productConfig.path}?${productConfig.query}`;
    }

    $(function () {
        GmAjax('POST', 'commonapi/initInfo', 'application/json', data, 'json', function (res) {
            if (res.status == 200 && res.payload) {
                console.log(res)
                payload = res.payload
                autoInfo = res.payload.pageUrlAutoPo // 定义为全局变量
                sucCode = res.payload.sucCode
                acName = res.payload.acName
                if (autoInfo.type === 1) {
                    // 辽宁移动京东e卡自动化审核页插码
                    if (p === '2c98ef5f947d3d98019482758a700028') {
                        lnlt_jscode(1, null, p)
                    }
                    if (p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002') {
                        // 将 script 标签添加到文档的 <head> 中
                        var json = {
                            "channelCode": p === "2c98ef5f926a105401926f0e0c02006c" ? "M3GP0001" : "M3GP0002",
                            /**渠道id，每个合作方独有的渠道id 必填*/
                            "sdkType": "https",
                            /**sdk加载类型，默认https，如注册的域名netid是http则填写http 必填*/
                            "domain": "https://cardstatic.zjhrnet.com/",
                            /**当前域名，用来匹配netids中的netid 必填*/
                            "option": { /*option具体说明请看文档*/
                                "umark": "Common2",
                                "color": ""
                            }
                        };
                        window.MiguSdk.getData(json, function (resultCode, msg, authSessionId) {
                            /**二次处理 */
                            console.log(resultCode);
                            console.log(msg);
                            console.log(authSessionId);
                            if (resultCode !== '0000') {
                                message.info("系统错误,请您稍后再试");
                                return;
                            }
                        });
                    }

                }
                let basic = res.payload.baseCode
                let script = document.createElement("script")
                try {
                    script.appendChild(document.createTextNode(basic))
                } catch (ex) {
                    script.text = basic
                }
                setTimeout(function () {
                    document.body.appendChild(script)
                }, 500)
                //设置页面内容
                const setPageInfo = async (info) => {
                    // console.log(info)
                    $('title').html(`${info.title}`)
                    // 创建一个新的 <link> 元素
                    var newLink = $('<link>')
                    // 设置 <link> 元素的属性
                    newLink.attr({
                        rel: 'shortcut icon',
                        // type: 'shortcut icon',
                        href: `${info.icon}`
                    })
                    $('head').append(newLink)
                    // 审核页
                    if (info.type === 1) {
                        // 页面开屏
                        if (info.popUps) {
                            $(".mask_bg img").attr("src", info.popUps)
                            $('.mask_bg').fadeIn().delay(1000).fadeOut()
                        } else {
                            $('.mask_bg').hide()
                        }
                        // 贵州联通插码逻辑 - 加载插件脚本
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=a9ffa39e0c274a879666d82cda29e66d&p=2c98ef5f967bd6a7019680d640bc0368&c=879078acc7634c6cad55c1b8f8b190df
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=4d64b1beb2a3463497d90f699108cfa5&p=2c98ef5f96c896f70196cc6a2eb00034&c=29335c5a68de4600b79d05d91d36d98f
                        // if (url.includes('page=a9ffa39e0c274a879666d82cda29e66d')) {
                        //     // 修改对象中属性值包含旧URL的内容
                        //     for (const key in info) {
                        //         if (Object.prototype.hasOwnProperty.call(info, key)) {
                        //             // 检查属性值是否包含旧的URL前缀
                        //             if (typeof info[key] === 'string' && info[key].includes('http://cl-autopage-material.oss-cn-hangzhou.aliyuncs.com')) {
                        //                 info[key] = info[key].replace('http://cl-autopage-material.oss-cn-hangzhou.aliyuncs.com', 'https://autopageoss.zjhrnet.com');
                        //             }
                        //         }
                        //     }
                        //     const scriptGzlt = document.createElement('script');
                        //     scriptGzlt.src = 'https://www.gz10010.shop/dlsplugin/plugin.js';
                        //     scriptGzlt.async = true;
                        //     scriptGzlt.type = 'text/javascript';
                        //     scriptGzlt.onload = function () {
                        //         // 插件脚本加载完成后再初始化
                        //         sth_ag.init({
                        //             channelCode: 'GZ-ZJHY',
                        //             productId: '855204290627',
                        //             materialId: 'hjf7zppco4'
                        //         }).then(res => {
                        //             console.log(res)
                        //             if (res.resultCode == '0000') {
                        //                 console.log('插件init成功')
                        //             } else {
                        //                 console.log('插件init失败')
                        //             }
                        //         })
                        //     };
                        //     document.head.appendChild(scriptGzlt);
                        // }
                        if (url.includes('page=a9ffa39e0c274a879666d82cda29e66d') || url.includes('page=4d64b1beb2a3463497d90f699108cfa5')) {
                            // 修改对象中属性值包含旧URL的内容
                            for (const key in info) {
                                if (Object.prototype.hasOwnProperty.call(info, key)) {
                                    // 检查属性值是否包含旧的URL前缀
                                    if (typeof info[key] === 'string' && info[key].includes('http://cl-autopage-material.oss-cn-hangzhou.aliyuncs.com')) {
                                        info[key] = info[key].replace('http://cl-autopage-material.oss-cn-hangzhou.aliyuncs.com', 'https://autopageoss.zjhrnet.com');
                                    }
                                }
                            }
                            const scriptGzlt = document.createElement('script');
                            scriptGzlt.src = 'https://www.gz10010.shop/dlsplugin/plugin.js';
                            scriptGzlt.async = true;
                            scriptGzlt.type = 'text/javascript';
                            // 使用三元运算符确定配置
                            const isPageA = url.includes('page=a9ffa39e0c274a879666d82cda29e66d');
                            const productId = isPageA ? '855204290627' : '855206236282';
                            const materialId = isPageA ? 'hjf7zppco4' : '77cyomnppk';
                            scriptGzlt.onload = function () {
                                // 插件脚本加载完成后再初始化
                                sth_ag.init({
                                    channelCode: 'GZ-ZJHY',
                                    productId: productId,
                                    materialId: materialId
                                }).then(res => {
                                    console.log(res)
                                    if (res.resultCode == '0000') {
                                        console.log('插件init成功')
                                    } else {
                                        console.log('插件init失败')
                                    }
                                })
                            };
                            document.head.appendChild(scriptGzlt);
                        }
                        // 安徽联通测试插码逻辑
                        if (c === '68d68a5ee80b4efba6b6ab841ad0648f') {
                            // $('.ah-jscode').attr('src', 'https://cardstatic.zjhrnet.com/front/h5-t-lam8t6n61fjtdd/index.html?p=2c98ef5f90152b090190156072060003&c=3a8bca0b3cea42299f37f7b7fafdd735');
                            // 1. 创建 iframe 元素并设置属性
                            const $iframe = $('<iframe>', {
                                src: 'https://cardstatic.zjhrnet.com/front/h5-t-lam8t6n61fjtdd/index.html?p=2c98ef5f90152b090190156072060003&c=3a8bca0b3cea42299f37f7b7fafdd735',
                                className: 'ah-jscode',
                                css: {
                                    width: '0',
                                    border: 'none',
                                    height: '0',
                                }
                            });
                            // 2. 插入到DOM中（例如插入到 body 末尾）
                            $('body').append($iframe);
                            setTimeout(() => {
                                ahLoginToken = sessionStorage.getItem('loginToken')
                                console.log(ahLoginToken)
                            }, 1000);
                        }

                        // 特殊页面输入框按钮缩小-活动说明修改-协议文字修改  https://cardstatic.zjhrnet.com/front/autopage/index.html?page=58c48165247247289cac63f7efd5bff6&p=2c98ef5f9565a2a001957dda224809e8&c=63612f607e3843e38693d7730a3880fe
                        if (url.includes('page=58c48165247247289cac63f7efd5bff6')) {
                            // 修改样式
                            $('.form-item').css({
                                'width': '6.2rem',
                                'height': '1rem'
                            });
                            $('.form-btn').css({
                                'width': '6rem',
                                'height': '1.1rem'
                            });
                            // 修改文本内容
                            $('.form-ys-p2').html('产品相关信息与');
                            $('.form-ys-p3').html('《产品说明》');
                            // 修改“等协议”文本
                            $('.agreement-text').text('协议')
                        }
                        // 特殊页面协议、资费字体颜色修改 https://cardstatic.zjhrnet.com/front/autopage/index.html?page=96e7bc410d6d41e0a4325d9426c325d5&p=2c98ef5f98acb11b0198bb2f48410077&c=a1ba8deba0ce4c11827d6d0018c30297
                        if (url.includes('page=96e7bc410d6d41e0a4325d9426c325d5')) {
                            $('.form-ys-radio,.form-ys-p1,.form-ys-p2,.form-ys-p3,.form-ys-p4,.agreement-text,.form-text').css('color', '#000')
                            $('.form-ys-radio').css('border', '0.01rem solid #000')
                        }
                        // 特殊页面按钮下方文字显示修改
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=e917d56069f34ed993bca007467a0a8d&p=2c98ef5f9815fdb40198160e27c40002&c=ed0652a6ad1249fbbd1103d1d396f905
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=a9799f2841ef481baeba219374279381&p=2c98ef5f981658290198178f0c44004c-%20&c=ed0652a6ad1249fbbd1103d1d396f905
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=4aa0f8b3374844f3aef0c61a589a07d8&p=2c98ef5f98f3ed370199042a198304bd&c=f169798e123b40018e5dec32864760a0
                        if (url.includes('page=e917d56069f34ed993bca007467a0a8d') || url.includes('page=a9799f2841ef481baeba219374279381') || url.includes('page=4aa0f8b3374844f3aef0c61a589a07d8')) {
                            $('.form-ys-p2').hide()
                            $('.agreement-text').hide()
                        }
                        // 特殊页面按钮下方文字显示修改
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=4b718542e67441d4be74ba9fc7a3861e&p=2c98ef5f982c6f3e019835dabef1097&c=ed0652a6ad1249fbbd1103d1d396f905
                        if (url.includes('page=4b718542e67441d4be74ba9fc7a3861e')) {
                            $('.form-ys-p3').text('《明星来电用户协议》')
                        }
                        // 特殊页面显示二次确认按钮高度
                        // if (url.includes('page=5b767e7604f84a8f8a2241455ea6e52f') || url.includes('page=6a1f0028ba784d52a2641a74d41fb77f') || url.includes('page=2d38e997dfda47518961e2d044ff0868') || url.includes('page=516c66dbcf2642e7adccfeb8c72d5e9e') || url.includes('page=ec0f87057465495aaaf79a1ef672eb6a')) {
                        //     $('.confirm-btn').css({
                        //         'height': '4.7rem'
                        //     })
                        // }
                        // 特殊页面二次确认按钮高度调整
                        // if (url.includes('page=e66b2e808f3c490197f1f0dcb838ba4a')
                        //     || url.includes('page=c9dbe294e8fc40b281a8a6e47c848c77')
                        //     || url.includes('page=dd830012fa0b4cf0802b7c3401d0c4f2')
                        //     || url.includes('page=87ede6ba63144ec893dc5b665fd8c47d')) {
                        //     $('.confirm-btn').css({
                        //         'margin-top': '5rem'
                        //     })
                        // }
                        // 特殊页面二次确认按钮高度调整
                        // https://h1.hzjieyingnet.cn/front/autopage/index.html?page=a5d3f80e2b6249599cb80e624c255171&p=2c98ef5f97d4da110197e8d6c32d0100&c=043da2d685d24599859be9894a74c159
                        // if (url.includes('page=a5d3f80e2b6249599cb80e624c255171')) {
                        //     $('.confirm-btn').css({
                        //         'margin-top': '2rem'
                        //     })
                        // }
                        // 特殊页面显示高度占比
                        if (url.includes('page=fed24cb884ae4056b0c422c646d4e833') || url.includes('page=031c69bf25864a86b9fbb14f5739da34')) {
                            $('.form').css({
                                'padding-top': '0'
                            })
                            $('.rule-title').hide()
                            $('.title_img').hide()
                        } else {
                            $('.form').css({
                                'padding-top': '.7rem'
                            })
                            $('.rule-title').show()
                            $('.title_img').show()
                        }
                        // 移动特殊页面显示解约条款
                        if (url.includes('page=5b767e7604f84a8f8a2241455ea6e52f') || url.includes('page=6a1f0028ba784d52a2641a74d41fb77f')
                            || url.includes('page=2d38e997dfda47518961e2d044ff0868') || url.includes('page=516c66dbcf2642e7adccfeb8c72d5e9e')
                            || url.includes('page=ec0f87057465495aaaf79a1ef672eb6a') || url.includes('page=a5d3f80e2b6249599cb80e624c255171')
                            || url.includes('page=8c98954a71884977be3ef7d1465592e5') || url.includes('page=38cbf159316946dcb001dd1c286d816a')
                            || url.includes('page=9266cbfef8c84c33881054b2ff5cabe5') || url.includes('page=7ff8ccfcbe0d41f39c9dc63c3b971d90')
                            || url.includes('page=75150ebb1a3246ee93199d5c3064c1b8') || url.includes('page=ca662cb30bd0484f9893a5c3c77bebda')
                            || url.includes('page=eb21c3b6a73643d09dec55fb08fb2912') || url.includes('page=1ffdd12ddce74e02bbc3632d2fa42754')
                            || url.includes('page=92e9cc1e6ac74dbcbefd9e8291129122')) {
                            $('.form-ys-p4').show()
                        } else {
                            $('.form-ys-p4').hide()
                        }
                        // 特殊页面调整《隐私政策》为《业务受理协议》https://cardstatic.zjhrnet.com/front/autopage/index.html?page=e06f54f21f4e48968955ad2588f18b24&p=2c98ef5f96fc72b701970beff4b3022d&c=6fb48aa06cb8446cb06c922be17c6672
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=5f1167f5aeb84ecca8573c1fb7286b2f&p=2c98ef5f9710cfb8019714ca7e210076&c=b80c820e4e3f466ba46ec351a1c57630
                        if (url.includes('page=e06f54f21f4e48968955ad2588f18b24') || url.includes('page=5f1167f5aeb84ecca8573c1fb7286b2f')) {
                            $('.form-ys-p2').text('《业务受理协议》')
                            $('.form-ys-p3').hide()
                            $('.agreement-text').hide()
                        }
                        // 悦蓝审核页客服悬浮窗和弹窗
                        if (url.includes('page=8358a928d14142f09cc4e595acb817ff')) {
                            $('.service-pop').show()
                        } else {
                            $('.service-pop').hide()
                            $('.popup-bg').hide()
                            $('.popup-close').hide()
                        }
                        // 秋末电信js插码 秋末-电信权益商城尊享会员-融合版
                        if (p === '2c98ef5f958d8944019593fe2da9008c') {
                            const qmdx = async () => {
                                const res = await axios.post('https://pojie.ai-me.cn/op-service/v1/zunxiang20240613/getSpNo/', {
                                    product_id: '31601001'
                                })
                                console.log(res.data.spNo)
                                AgentControler.getSpn(res.data.spNo)
                            }
                            setTimeout(() => {
                                qmdx()
                            }, 300);
                        }
                        // 秋末电信js插码 秋末-山西电信-精选权益月包19.9元本地权益-融合版
                        if (p === '2c98ef5f96142f9f0196198fd96e00b2') {
                            AgentControler.getSpn('SPN202502211455124533234')
                        }
                        // 读赞-重庆电信-15.9元视频彩铃+视听权益包-融合版
                        if (p === '2c98ef5f96b384ba0196b408d9020083') {
                            AgentControler.getSpn('SPN202503211038294233336')
                        }
                        // 读赞-重庆电信-19视听会员5G流量包-融合版
                        if (p === '2c98ef5f96b384ba0196b413380b0099') {
                            AgentControler.getSpn('SPN202503031035315013890')
                        }
                        // 读赞-电信-尊享会员（全国）-融合版
                        if (p === '2c98ef5f96b384ba0196b3f90fe30045') {
                            AgentControler.getSpn('SPN202311080958129326510')
                        }

                        // 特殊页面调整文字描述
                        if (url.includes('page=72a60e827a794b57b07ff7732922ee23')) {
                            $('.rule-title .t').html('订购说明')
                        } else if (url.includes('page=58c48165247247289cac63f7efd5bff6')) {
                            $('.rule-title .t').html('产品说明')
                        } else {
                            $('.rule-title .t').html('活动说明')
                        }
                        if (info.headerUrl) {
                            $(".top-img img").attr("src", info.headerUrl)
                        }
                        if (info.countdown) {
                            $('.form-scrolls').show()
                        } else {
                            $('.form-scrolls').hide()
                        }
                        $('.form-scroll-inner').hide()
                        if (info.formUrl) {
                            $('body').css('backgroundColor', `${info.formUrl}`)
                        }
                        if (info.guidelines) {
                            $('.form_img_1').show()
                            if (info.oneGuidelinesUrl) {
                                $(".form_img_1").attr("src", info.oneGuidelinesUrl)
                            }
                            if (info.twoGuidelinesUrl) {
                                $(".form_img_2").attr("src", info.twoGuidelinesUrl)
                            }
                            if (info.threeGuidelinesUrl) {
                                $(".form_img_3").attr("src", info.threeGuidelinesUrl)
                            }
                        } else {
                            $('.form_img_1').hide()
                            $('.form_img_2').hide()
                            $('.form_img_3').hide()
                        }
                        if (info.buttonUrl) {
                            $(".form-btn").css({
                                background: `url(${info.buttonUrl}) no-repeat`,
                                width: "6.8rem",
                                // height: "1.46rem",
                                backgroundSize: "contain",
                                backgroundPosition: "center",
                            });
                        }
                        if (!info.showAgreement) {
                            $(".form-ys").hide()
                        }
                        if (info.tariffDescription) {
                            if (p === '2c98ef5f961ec9fe01961eee1c450009') {
                                $(".form-text").html(`<a href="tel:400-1050299" style="color:#fff">明星来电一档明星C包20元客服电话:400-1050299</a>`)
                            } else if (p === '2c98ef5f96142f9f0196199080d200b3') {
                                $(".form-text").html(`<a href="tel:400-1050299" style="color:#fff">书香伴读合约年包(合约月费版)10元客服电话:400-1050299</a>`)
                            } else {
                                $(".form-text").html(info.tariffDescription)
                            }
                        }

                        if (info.rightsUrl) {
                            $(".pro-img img").attr("src", info.rightsUrl)
                        }
                        // 当活动说明少于20个中文字符时，隐藏活动说明文字及图标
                        if (info.activityDescription) {
                            // 使用正则表达式匹配中文字符
                            const chineseCharCount = (info.activityDescription.match(/[\u4e00-\u9fa5]/g) || []).length;
                            if (info.activityDescription.includes('活动说明') && chineseCharCount < 20 || chineseCharCount < 20) {
                                $('.rule-title').hide()
                                $('.title_img').hide()
                            } else {
                                $(".tips-text").html(info.activityDescription)
                            }
                        }
                        if (info.channelCodeUrl) {
                            $(".sq-img img").attr("src", info.channelCodeUrl)
                        }
                        if (info.secondPopUrl) {
                            $('.confirm-new').css({
                                "background-image": `url("${info.secondPopUrl}")`,
                            })
                        }
                        // 咪咕数媒-咪咕悦读畅享会员9.9元-融合版  咪咕数媒-咪咕短剧超级会员20元-融合版
                        if (p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002') {
                            $('.form-item-code').hide()
                        } else {
                            $('.form-item-code').show()
                        }
                        // 特殊页面显示400拨号功能
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=14fafea41ad148fcbfcaf212b93f9cfc&p=2c98ef5f9858ec19019859ffa6c0019d&c=2463068a490b4aa6896247285a23c65e
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=ac062dab6ad548059974ca1b95d67b5d&p=2c98ef5f9858ec19019859c74e400191&c=94bc7bb191eb43ec86e4c624cb30022d
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=03e298715a934c65abd996a1d989d30a&p=2c98ef5f9858ec19019859c9c1f70193&c=68793190aaef476289b967d7aa654aa1
                        if (url.includes('page=14fafea41ad148fcbfcaf212b93f9cfc')
                            || url.includes('page=ac062dab6ad548059974ca1b95d67b5d')
                            || url.includes('page=03e298715a934c65abd996a1d989d30a')) {
                            $('.tel-phone').text("客服电话：************")
                            $('.tel-phone').show()
                        } else {
                            $('.tel-phone').hide()
                        }
                    }
                    if (info.type === 2) {
                        if (info.popUps) {
                            $(".mask_bg img").attr("src", info.popUps)
                            $('.mask_bg').fadeIn().delay(1000).fadeOut()
                        } else {
                            $('.mask_bg').hide()
                        }
                        // 特殊页面添加播放视频操作
                        if (url.includes('bdf5b6809d6b4ee18cc042f53d8f139d')) {
                            // 创建 top_video 元素
                            const $topVideo = $('<div>').addClass('top_video');
                            // 添加元素到页面
                            $('.page').append($topVideo);
                            // 设置 top_video 的样式
                            $('.top_video').css({
                                width: '3.2rem',
                                height: '4.8rem',
                                position: 'absolute',
                                top: '2.87rem',
                                right: '2.15rem',
                                borderRadius: '.2rem',
                                // backgroundColor: 'rgba(0, 0, 0, 0.7)'
                            });
                            // 创建 video 元素
                            const $video = $('<video>')
                                .attr('src', 'https://oss.zjhrnet.com/img/cunliang/liangtong/auto/video.mp4') // 替换为实际的视频地址
                                .attr('controls', true) // 显示播放控制条
                                .attr('autoplay', true) // 设置自动播放
                                .attr('muted', true) // 静音播放，某些浏览器要求静音才能自动播放
                                .css({
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: '.2rem',
                                    objectFit: 'cover' // 视频填充方式
                                });
                            // 将 video 元素添加到 top_video 容器中
                            $topVideo.append($video);
                            // 手动设置音量为 0
                            $video.on('loadedmetadata', function () {
                                this.volume = 0;
                            });
                            // 创建一个新的样式标签
                            const style = document.createElement('style');
                            style.innerHTML = `
                                .form-input::placeholder {
                                    color: red !important;
                                }
                            `;
                            document.head.appendChild(style);
                            $('.form-scrolls').show()
                        }
                        // 特殊页面二次确认按钮高度调整
                        // https://cardstatic.zjhrnet.com/front/autopage/index.html?page=a64fda888bb240b5aeacd196bca6e77e&p=2c98ef5f8c3e037c018c6b29ba0300b2&c=0a6db24b8892483a81311eecaeea2492
                        // if (url.includes('page=a64fda888bb240b5aeacd196bca6e77e')) {
                        //     $('.confirm-btn').css({
                        //         'margin-top': '5rem'
                        //     })
                        // }
                        if (info.formUrl) {
                            $('body').css('backgroundColor', `${info.formUrl}`)
                        }
                        if (info.headerUrl) {
                            $(".top-img img").attr("src", info.headerUrl)
                        }
                        if (info.countdown) {
                            $('.form-scrolls').show()
                        } else {
                            $('.form-scrolls').hide()
                        }
                        if (info.guidelines) {
                            $('.form_img_1').show()
                            if (info.oneGuidelinesUrl) {
                                $(".form_img_1").attr("src", info.oneGuidelinesUrl)
                            }
                            if (info.twoGuidelinesUrl) {
                                $(".form_img_2").attr("src", info.twoGuidelinesUrl)
                            }
                            if (info.threeGuidelinesUrl) {
                                $(".form_img_3").attr("src", info.threeGuidelinesUrl)
                            }
                        } else {
                            $('.form_img_1').hide()
                            $('.form_img_2').hide()
                            $('.form_img_3').hide()
                        }
                        if (info.buttonUrl) {
                            $(".form-btn").css({
                                background: `url(${info.buttonUrl}) no-repeat`,
                                width: "6.8rem",
                                height: "2rem",
                                backgroundSize: "contain",
                                backgroundPosition: "center",
                            });
                        }
                        if (info.showAgreement) {
                            $(".form-ys").show()
                        }
                        if (info.tariffDescription) {
                            $(".form-text").html(info.tariffDescription)
                        }
                        if (info.rightsUrl) {
                            $(".pro-img img").attr("src", info.rightsUrl)
                        }
                        // 当活动说明少于20个中文字符时，隐藏活动说明文字及图标
                        if (info.activityDescription) {
                            // 使用正则表达式匹配中文字符
                            const chineseCharCount = (info.activityDescription.match(/[\u4e00-\u9fa5]/g) || []).length;
                            if (info.activityDescription.includes('活动说明') && chineseCharCount < 20 || chineseCharCount < 20) {
                                $('.rule-title').hide()
                                $('.title_img').hide()
                            } else {
                                $(".tips-text").html(info.activityDescription)
                            }
                        }
                        if (info.channelCodeUrl) {
                            $(".sq-img img").attr("src", info.channelCodeUrl)
                        }
                        if (info.secondPopUrl) {
                            $('.confirm-new').css({
                                "background-image": `url("${info.secondPopUrl}")`,
                            })
                        }

                    }
                    $('body').css('visibility', 'visible')
                    return Promise.resolve(true)
                }
                const autoLinkResult = () => {
                    $(".load_bg").show()
                    const data = JSON.stringify({
                        acid: c,
                        mobile: phone,
                        modeId: autoInfo.autoModeId,
                        // modeId: '2c98ef5f91bfce910191ef7e8bb90502',
                        pid: p,
                    })
                    GmAjax('POST', 'clcommonapi/autoLink', 'application/json', data, 'json', (res) => {
                        if (res.status === 200) {
                            $(".load_bg").hide()
                            autoLinkInfo = res.payload
                            const {payload} = res
                            if (payload.productList && payload.productList.length > 0) {
                                productList = payload.productList
                                infoObj = payload.productList[0]
                                const checkResult = checkAndCloseSpecialProduct(infoObj, autoLinkInfo, packageName);
                                if (checkResult) {
                                    // 没通过校验直接去长尾点位2 ext1 0
                                    pageData_auto(c, phone, p, 2, 0);
                                    getAd('order_error', 2, 2);
                                    return;
                                }
                                // 特殊链接移动电信号码直接下发验证码 2025-02-07
                                // updateUI(infoObj)
                                // p = infoObj.fusionProId
                                // pageData_auto(c, phone, p, 2,)
                                p = infoObj.fusionProId
                                console.log(infoObj)
                                // 通过校验点位2 ext1 1
                                if (infoObj.telcoType === "7") {
                                    console.log("三方支付")
                                } else {
                                    pageData_auto(c, phone, p, 2, 1)
                                }
                                if ((c === 'e4f671bcc3554c2db70ec63e7884d3f9'
                                        || c === '20a38e61ba3b49a9a915d068d3c1ebf5'
                                        || c === '67a0dfd156154dbcbbfb1661a952ee28'
                                        || c === '0c7724f7bcfd44a4922a949aceb866cc'
                                        || c === '08d2872950b84287ad0042db9ed152f6'
                                        || c === '9cecd8b3ab574065bfdeeda3d0692c2b'
                                        || c === 'a0b67cac2d714d08901dc9e133187d05')
                                    && (autoLinkInfo.cmcc === '移动' || autoLinkInfo.cmcc === '电信')) {
                                    mg_checkPhone(infoObj)
                                } else {
                                    if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                                        || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                                        || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                                        || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                                        || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                                        || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                                        || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                                        || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                                        || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                                        || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                                    ) {
                                        const productConfig = getMerchantsInfo(p);
                                        console.log(productConfig);
                                        sessionStorage.setItem('gdydpId', p)
                                        sessionStorage.setItem('gdydHost', productConfig.domain)
                                        // 广东移动插码初始化
                                        loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangdong/gdp-full.js')
                                            .then(() => {
                                                console.log('广东移动插码 已加载完成');
                                                // 初始化gdp
                                                gdp('init', '8d2279a5e2f18b7c', '9d59ac736f01c688', {
                                                    host: 'collect.gmcc.net',
                                                    compress: false,
                                                    hashtag: true,
                                                    debug: true
                                                });
                                                gdp('setGeneralProps', {
                                                    "global_merchantsId_var": productConfig.merchantsId,
                                                    "global_merchants_var": productConfig.merchantsName,
                                                    "global_apiId_var": "smscodechkodcommitorder",
                                                    "global_apiName_var": "短信验证及订单提交",
                                                    "global_merchantsIdCity_var": "省统"
                                                });

                                                // 先加载crypto-js
                                                return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                                            })
                                            .then(() => {
                                                console.log('crypto-js 加载完成');
                                                // 加载配置文件
                                                return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangDongInsertCodeConfig.js');
                                            })
                                            .then(() => {
                                                console.log('广东移动插码配置 已加载完成');
                                                window.gdGxInsertCodeReady = true;
                                                event_upload("页面浏览", null, null, encryptAES(phone), null, null, null, p);
                                                event_upload("输入电话号码", null, null, encryptAES(phone), null, null, null, p);
                                            })
                                            .catch(error => {
                                                console.error('脚本加载失败:', error);
                                                window.gdGxInsertCodeReady = true;
                                            });
                                    }

                                    if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                                        sessionStorage.setItem('gxydpId', p)
                                        loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangxi/gdp-full.js')
                                            .then(() => {
                                                console.log('广西移动插码 已加载完成');
                                                // 广西移动插码初始化
                                                gdp('init', '86e27c5fd2e5e4cd', '8bcdeab9a38bdeef', {
                                                    serverUrl: 'www.gx.10086.cn',// 数据发送地址。
                                                    compress: false,// 开启数据压缩模式。
                                                    hashtag: true,// 开启hash解析
                                                    scheme: 'https',
                                                    debug: true
                                                });
                                                // 广西移动全局变量采集
                                                gdp('setGeneralProps', {
                                                    "XY_global_merchantsId": "21874", // 店铺编号
                                                    "XY_global_merchants": "中网数科（成都）科技有限公司", // 店铺名称
                                                    "XY_global_apiName": "流量，套餐业务办理接口", // 调用接口名称；传产品最后受理的接口名称
                                                    "XY_global_apiUrl": "https://cardstatic.zjhrnet.com/cardfr/clcommonapi/createOrder" // 生产环境调用接口地址；传产品最后受理的调用接口地址
                                                });

                                                // 先加载crypto-js
                                                return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                                            })
                                            .then(() => {
                                                console.log('crypto-js 加载完成');
                                                // 加载配置文件
                                                return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangXiInsertCodeConfig.js');
                                            })
                                            .then(() => {
                                                console.log('广西移动插码配置 已加载完成');
                                                window.gdGxInsertCodeReady = true;
                                                guangXi_event_upload(type = "页面浏览", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                                guangXi_event_upload(type = "输入电话号码", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                            })
                                            .catch(error => {
                                                console.error('脚本加载失败:', error);
                                                window.gdGxInsertCodeReady = true;
                                            });
                                    }
                                    updateUI(infoObj)
                                }
                                if (infoObj.recommend) {
                                    if (infoObj.telcoType === "7") {
                                        console.log("三方支付")
                                    } else {
                                        pageData_auto(c, phone, p, 11, 1)
                                    }
                                }
                            } else {
                                pageData_auto(c, phone, p, 2, 0)
                                getAd('order_error', 2, 2)
                            }
                        } else {
                            $(".load_bg").hide()
                            message.info(res.error)
                            getAd('order_error', 2, 2)
                        }
                    });
                }
                setTimeout(function () {
                    // 调用 pageData_auto 函数
                    if (url.includes('c623f8f1b4c3416488bdfe0e730d1191')) {
                        if (pos.includes('__POS__')) {
                            if (match && match[0]) {
                                packageName = match[0]
                            } else {
                                // console.log("No match found");
                            }
                        } else {
                            packageName = pos
                        }
                        pageData_auto(c, phone, p, 1, userAgent, '', '', packageName)
                    } else {
                        if (match && match[0]) {
                            packageName = match[0]
                        } else {
                            // console.log("No match found");
                        }
                        pageData_auto(c, phone, p, 1, userAgent, '', '', packageName)
                    }
                    setPageInfo(autoInfo)
                }, 700);
                // 页面带号输
                let upPagePhone = getQueryString('phone') || '';
                if (upPagePhone && isPhone(upPagePhone)) {
                    $('#phone').val(upPagePhone);
                    phone = upPagePhone;
                    if (autoInfo?.type === 2) {
                        setTimeout(autoLinkResult, 700);
                    }
                }
                // 获取 pageId 的函数
                const getPageId = () => {
                    return new Promise((resolve, reject) => {
                        let pageUrl = location.href
                        if (pageUrl.includes('?page')) {
                            pageUrl = pageUrl.split('&c=')[0]
                        }
                        $.ajax({
                            type: 'GET',
                            url: 'https://cardstatic.zjhrnet.com/cardfr/commonapi/getPageId',
                            data: {pageUrl: pageUrl},
                            dataType: 'json',
                            success: (res) => {
                                const pageId = res.payload.pageId || ''
                                // console.log('Fetched pageId:', pageId)
                                resolve(pageId)
                            },
                            error: (jqXHR, textStatus, errorThrown) => {
                                // console.error('Error fetching pageId:', textStatus, errorThrown)
                                reject(new Error('Failed to fetch pageId'))
                            }
                        })
                    })
                }
                getPageId().then(res => {
                    pageUrlId = res
                    // console.log(pageUrlId)
                }).catch(error => {
                    console.error('Error:', error)
                })

                //新增点位入参
                const getAd = async function (ad_position, error_position, jump_number) {
                    // console.log(autoLinkInfo)
                    $('.provice-fixed').show()
                    // pageData_auto(c, phone, p, 10,)
                    if (autoLinkInfo.cmcc === '移动') {
                        // $('.provice-fixed').show()
                        setTimeout(function () {
                            $('.provice-fixed').hide()
                            $(".load_bg,.loading").hide()
                        }, 2000)
                        Ad_Show(`${phone}`, `${autoLinkInfo.province}`, `${autoLinkInfo.cmcc}`, `${ad_position}`, '无法办理该业务', '融合产品', 'FCS', c, p, error_position, '', packageName, jump_number)
                    } else {
                        Ad_Show(`${phone}`, `${autoLinkInfo.province}`, `${autoLinkInfo.cmcc}`, `${ad_position}`, '无法办理该业务', '融合产品', 'LLG', c, p, error_position, '', packageName, jump_number)
                    }
                }


                const updateUI = (infoObj, value2) => {
                    p = infoObj.fusionProId
                    // 特殊链接移动电信号码直接下发验证码 2025-02-07
                    // if (infoObj.sendCode === 1) {
                    //     mg_checkPhone()
                    // }
                    // 移动电信特殊处理
                    if ((value2 === 'specialShow')) {
                        console.log(infoObj)
                    } else {
                        if (infoObj.sendCode === 1) {
                            // 秋末电信js插码 秋末-电信权益商城尊享会员-融合版   // 秋末电信js插码 秋末-山西电信-精选权益月包19.9元本地权益-融合版
                            // 读赞-重庆电信-15.9元视频彩铃+视听权益包-融合版
                            // 读赞-重庆电信-19视听会员5G流量包-融合版
                            // 读赞-电信-尊享会员（全国）-融合版
                            if (p === '2c98ef5f958d8944019593fe2da9008c'
                                || p === '2c98ef5f96142f9f0196198fd96e00b2'
                                || p === '2c98ef5f96b384ba0196b408d9020083'
                                || p === '2c98ef5f96b384ba0196b413380b0099'
                                || p === '2c98ef5f96b384ba0196b3f90fe30045') {
                                setTimeout(() => {
                                    $(".load_bg").show();
                                    mg_checkPhone()
                                }, 2000);
                            } else {
                                mg_checkPhone()
                                return
                            }
                        }
                    }
                    // 页面开屏
                    if (infoObj.popUps) {
                        $(".mask_bg img").attr("src", infoObj.popUps)
                        $('.mask_bg').fadeIn().delay(1000).fadeOut()
                    } else {
                        $('.mask_bg').hide()
                    }
                    // 号卡跳转链接
                    if (p === '2c98ef5f951d6b980195229c3da50074') {
                        let redNum = Math.floor(Math.random() * 10) + 1
                        if (redNum < 4) {
                            location.href = `https://cardstatic.zjhrnet.com/front/h5-t-dkc6yry4pl1aat/index.html?p=2c98ef5f9443c25901945e39ad1601b6&c=${c}&clickid=${clickId}&a_oId=${a_oId}&phone=${phone}`
                            return
                        } else {
                            getAd('order_error', 2, 2)
                        }
                    }
                    // 自动化跳转
                    if (infoObj.pageUrl) {
                        if (infoObj.pageUrl.includes('?page')) {
                            location.href = `${infoObj.pageUrl}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&phone=${phone}`
                        } else {
                            if (p === '2c98ef5f967bd6a7019680d640bc0368') {
                                location.href = `${infoObj.pageUrl}?p=2c98ef5f967bd6a7019680d640bc0368&c=6710d43fd6974ababd222456070459f0&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&phone=${phone}&acid=${c}`
                            } else if (p === '2c98ef5f951d6b980195275a913b00fa') {
                                location.href = `${infoObj.pageUrl}?p=2c98ef5f951d6b980195275a913b00fa&c=cb31c543a6c64f5ea7728a650efa2f44&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&phone=${phone}&acid=${c}`
                            } else {
                                location.href = `${infoObj.pageUrl}?p=${p}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&phone=${phone}`
                            }
                        }
                        return
                    }
                    // 页面特殊处理
                    if (url.includes('bdf5b6809d6b4ee18cc042f53d8f139d')) {
                        $('.top_video').show()
                        const fusionProIdDjMap = [
                            '2c98ef5f922c56a201923d042a6001fe',
                            '2c98ef5f916eee1a01917dbe1d440226',
                            '2c98ef5f8fe11e0a018ff15cc71e017b',
                            '2c98ef5f8fc8a92c018fcc28ee050003'
                        ]
                        if (fusionProIdDjMap.includes(infoObj.fusionProId)) {
                            $('.top_video').css({
                                width: '3.2rem',
                                height: '4.7rem',
                                position: 'absolute',
                                top: '3.3rem',
                                right: '2.15rem',
                                borderRadius: '.2rem',
                            });
                        } else {
                            $('.top_video').hide()
                        }
                        $('.form-scrolls').show()
                        $('.form').css({
                            'padding-top': '0.5rem'
                        })
                    }

                    // 咪咕数媒初始化逻辑(破解版pid-非破解版pid)
                    if (p === '2c98ef5f935272250193629d40ec009d' || p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002' || p === '2c98ef5f9488f97f01948dd03a950086') {
                        // 将 script 标签添加到文档的 <head> 中
                        let channelCode;
                        if (p === '2c98ef5f935272250193629d40ec009d' || p === '2c98ef5f926a105401926f0e0c02006c') {
                            channelCode = "M3GP0001";
                        } else if (p === '2c98ef5f9482ee97019482f3a8330002' || p === '2c98ef5f9488f97f01948dd03a950086') {
                            channelCode = "M3GP0002";
                        }
                        var json = {
                            "channelCode": channelCode, /**渠道id，每个合作方独有的渠道id 必填*/
                            "sdkType": "https", /**sdk加载类型，默认https，如注册的域名netid是http则填写http 必填*/
                            "domain": "https://cardstatic.zjhrnet.com/", /**当前域名，用来匹配netids中的netid 必填*/
                            "option": { /*option具体说明请看文档*/
                                "umark": "Common2",
                                "color": ""
                            }
                        };
                        console.log(json)
                        window.MiguSdk.getData(json, function (resultCode, msg, authSessionId) {
                            /**二次处理 */
                            console.log(resultCode);
                            console.log(msg);
                            console.log(authSessionId);
                            if (resultCode !== '0000') {
                                message.info("系统错误,请您稍后再试");
                                return;
                            }
                            console.log(resultCode)
                        });
                    }

                    // // 广东联通插码逻辑
                    // // 广东联通-畅享权益流量月包19.9元
                    // if (p ==='2c98ef5f8f77411b018f79f683300055') {
                    //     // $('.ah-jscode').attr('src', 'http://127.0.0.1:5501/H5%E6%8A%95%E6%94%BE%E8%90%BD%E5%9C%B0%E9%A1%B5/h5-t-noromucjmtiaij/index.html')
                    //     setTimeout(() => {
                    //         const gdlt_iframe = $('.ah-jscode')[0];
                    //         console.log(gdlt_iframe)
                    //         if (gdlt_iframe && gdlt_iframe.contentWindow) {
                    //             gdlt_iframe.contentWindow.postMessage(
                    //                 { type: 'PHONE', data: phone },
                    //                 window.location.origin
                    //             );
                    //         } else {
                    //             console.error('gdlt_iframe 或 contentWindow 未正确加载');
                    //         }
                    //     }, 700);
                    //     window.addEventListener('message', (event) => {
                    //         // 安全校验：确保消息来自同源
                    //         if (event.origin !== window.location.origin) return;
                    //         const { type, data } = event.data;
                    //         switch (type) {
                    //           case 'UUID1':
                    //             uuid1 = data;
                    //             console.log(uuid1)
                    //             // callSendVerificationCode(uuid1); // 调用下发验证码函数
                    //             break;
                    //           case 'UUID2':
                    //             uuid2 = data;
                    //             console.log(uuid2)
                    //             // callPlaceOrder(uuid2); // 调用下单函数
                    //             break;
                    //         }
                    //     });
                    // }
                    // 广东移动特殊跳转链接处理
                    if (p === '2c98ef5f94170fed01943a8ddb2c024d') {
                        window.location.href = `https://10086.haoma.com/stock/yd_active_user_v2?itemCode=j4n0Ukfp6ue&sourceCode=021796&oaid=${c}`
                        return
                    }
                    // 英雄联盟-融合版  穿越火线-融合版  使命召唤-融合版
                    if (p === '2c98ef5f948dfd5d019492309c7b015a' || p === '2c98ef5f9496c3780194979ab8ad00d0' || p === '2c98ef5f957f22e80195896c8d100384') {
                        let data = JSON.stringify({
                            pid: p,
                            acid: c,
                            mobile: phone,
                            entranName: packageName,
                            clickid: clickId,
                            a_oId: a_oId,
                        })
                        GmAjax(
                            "POST",
                            "clcommonapi/getYxlmUrl",
                            "application/json",
                            data,
                            "json",
                            function (res) {
                                console.log(res);
                                if (res.status == 200 || res.status == 201) {
                                    location.href = res.payload.url
                                }
                            }
                        );

                    }
                    // 秋末电信js插码 秋末-电信权益商城尊享会员-融合版
                    if (p === '2c98ef5f958d8944019593fe2da9008c') {
                        const qmdx = async () => {
                            const res = await axios.post('https://pojie.ai-me.cn/op-service/v1/zunxiang20240613/getSpNo/', {
                                product_id: '31601001'
                            })
                            console.log(res.data.spNo)
                            AgentControler.getSpn(res.data.spNo).then(() => {
                                // 秋末电信js插码
                                AgentControler.checkPhoneAndCheck(phone);
                                AgentControler.checkVoucherNumber().then(res => {
                                    console.log(res);// 流水号（发送验证码和下单时作为入参voucherNumber，下单校验需要）
                                    console.log(res.voucherNumber);
                                    voucherNumber = res.voucherNumber
                                }).catch(err => {
                                    console.log(err);// 错误信息
                                })

                            })
                        }
                        qmdx()
                    }
                    // 秋末电信js插码 秋末-山西电信-精选权益月包19.9元本地权益-融合版
                    if (p === '2c98ef5f96142f9f0196198fd96e00b2') {
                        AgentControler.getSpn('SPN202502211455124533234').then(() => {
                            // 秋末电信js插码
                            AgentControler.checkPhoneAndCheck(phone);
                            AgentControler.checkVoucherNumber().then(res => {
                                console.log(res);// 流水号（发送验证码和下单时作为入参voucherNumber，下单校验需要）
                                console.log(res.voucherNumber);
                                voucherNumber = res.voucherNumber
                            }).catch(err => {
                                console.log(err);// 错误信息
                            })

                        })
                    }
                    // 读赞-重庆电信-15.9元视频彩铃+视听权益包-融合版
                    if (p === '2c98ef5f96b384ba0196b408d9020083') {
                        AgentControler.getSpn('SPN202503211038294233336').then(() => {
                            // 读赞js插码
                            AgentControler.checkPhoneAndCheck(phone);
                            AgentControler.checkVoucherNumber().then(res => {
                                console.log(res);// 流水号（发送验证码和下单时作为入参voucherNumber，下单校验需要）
                                console.log(res.voucherNumber);
                                voucherNumber = res.voucherNumber
                            }).catch(err => {
                                console.log(err);// 错误信息
                            })
                        })
                    }

                    // 读赞-重庆电信-19视听会员5G流量包-融合版
                    if (p === '2c98ef5f96b384ba0196b413380b0099') {
                        AgentControler.getSpn('SPN202503031035315013890').then(() => {
                            // 读赞js插码
                            AgentControler.checkPhoneAndCheck(phone);
                            AgentControler.checkVoucherNumber().then(res => {
                                console.log(res);// 流水号（发送验证码和下单时作为入参voucherNumber，下单校验需要）
                                console.log(res.voucherNumber);
                                voucherNumber = res.voucherNumber
                            }).catch(err => {
                                console.log(err);// 错误信息
                            })

                        })
                    }

                    // 读赞-电信-尊享会员（全国）-融合版
                    if (p === '2c98ef5f96b384ba0196b3f90fe30045') {
                        AgentControler.getSpn('SPN202311080958129326510').then(() => {
                            // 读赞js插码
                            AgentControler.checkPhoneAndCheck(phone);
                            AgentControler.checkVoucherNumber().then(res => {
                                console.log(res);// 流水号（发送验证码和下单时作为入参voucherNumber，下单校验需要）
                                console.log(res.voucherNumber);
                                voucherNumber = res.voucherNumber
                            }).catch(err => {
                                console.log(err);// 错误信息
                            })

                        })
                    }

                    // 辽宁移动插码
                    if (p === '2c98ef5f94170fed01941a58e4bf002f' || p === '2c98ef5f94170fed01941a57bac2002e' || p === '2c98ef5f947d3d98019482758a700028' || p === '2c98ef5f94b1cf770194d504518702fa') {
                        lnlt_jscode(1, null, p)
                        console.log('--------------------')
                    }
                    // 安徽联通js插码  ----- 安徽联通-25元视彩流量组合包-融合版
                    if (p === '2c98ef5f916eee1a01917dbe1d440226') {
                        // 1. 创建 iframe 元素并设置属性
                        const $iframe = $('<iframe>', {
                            src: 'https://cardstatic.zjhrnet.com/front/h5-t-q1y1qxjudync6j/index.html?p=2c98ef5f916eee1a01917dbe1d440226&c=4c00d61b2b6f4dfb827d5728801a88b3',
                            className: 'ah-jscode',
                            css: {
                                width: '0',
                                border: 'none',
                                height: '0',
                            }
                        });
                        // 2. 插入到DOM中（例如插入到 body 末尾）
                        $('body').append($iframe);
                        setTimeout(() => {
                            ahLoginToken = sessionStorage.getItem('loginToken')
                            console.log(ahLoginToken)
                        }, 1000);
                    }
                    // // 江苏纳管产品秋末
                    // if (p === '2c98ef5f922c56a20192415bc182031d') {
                    //     location.href = `https://qmlt.qiumo.net.cn/micropage-touliu/pages/otherMp/index?pageId=9072013224829468&channelId=11&s=100000292&from=hrtg001&_tlu=${phone}&p=2c98ef5f982b80ab01982bd9a2560087&fusionPid=2c98ef5f982b80ab01982bdb5d0e0088&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&sourcePackage=${packageName}&unionSite=${unionSite}`
                    //     return
                    // }
                    // 江苏纳管产品跳转链接 融合pid/实际pid
                    const fusionProIdToProductIdMap = {
                        '2c98ef5f92e632840192ffeb171d028f': '2c98ef5f92e632840192ffea3cdb028e',
                        '2c98ef5f922c56a20192415d40ce031e': '2c98ef5f92b770f40192b85838610010',
                        '2c98ef5f922c56a201924155e704031c': '2c98ef5f92b8b6c80192b8c47da30003',
                        '2c98ef5f922c56a20192415bc182031d': '2c98ef5f92b8b6c80192b8bdc3010001',
                        '2c98ef5f9274db52019289a60fe00179': '2c98ef5f92b8b6c80192b8c243450002',
                        '2c98ef5f93e329150193f2cd3e23010a': '2c98ef5f93e329150193f2c861530107',
                        '2c98ef5f93e329150193f2ce1174010b': '2c98ef5f93e329150193f2cb871f0108',
                        '2c98ef5f94ef0e8c0194f94e7c3e0129': '2c98ef5f94ef0e8c0194f9466f5c0128',
                        '2c98ef5f951d6b9801952740325a00d6': '2c98ef5f951d6b980195273f89ad00d5',
                        '2c98ef5f9744a04e019752828c820073': '2c98ef5f9744a04e01975267065c006a',
                        // '2c98ef5f97f780f40197f7f1a6980009': '2c98ef5f97f780f40197f7ded6850002',
                        '2c98ef5f985f67810198648a25060075': '2c98ef5f985f6781019864892f120074',
                        // '2c98ef5f982b80ab01982bdb5d0e0088': '2c98ef5f982b80ab01982bd9a2560087',
                        '2c98ef5f989c41f90198a226fd5f048b': '2c98ef5f989c41f90198a20b246c0484',
                        '2c98ef5f989c41f90198a1fa874c0482': '2c98ef5f989c41f90198a1f72a890481',
                    };
                    // 实际pid/跳转链接pageId
                    const productIdToPageIdMap = {
                        '2c98ef5f92e632840192ffea3cdb028e': '9021111132417736',
                        '2c98ef5f92b770f40192b85838610010': '9021111031767459',
                        '2c98ef5f92b8b6c80192b8c47da30003': '9021111070285156',
                        '2c98ef5f92b8b6c80192b8bdc3010001': '9021111182117522',
                        '2c98ef5f92b8b6c80192b8c243450002': '9021111050477150',
                        '2c98ef5f93e329150193f2c861530107': '9021111163477162',
                        '2c98ef5f93e329150193f2cb871f0108': '9012419091535401',
                        '2c98ef5f94ef0e8c0194f9466f5c0128': '9021316215798968',
                        '2c98ef5f951d6b980195273f89ad00d5': '9030717083536626',
                        '2c98ef5f9744a04e01975267065c006a': '9060915153079093',
                        // '2c98ef5f97f780f40197f7ded6850002': '9071114301890961',
                        '2c98ef5f985f6781019864892f120074': '9080115180683702',
                        // '2c98ef5f982b80ab01982bd9a2560087': '9072013224829468',
                        '2c98ef5f989c41f90198a20b246c0484': '9081311310172744',
                        '2c98ef5f989c41f90198a1f72a890481': '9081311363615575',
                    }
                    if (fusionProIdToProductIdMap[infoObj.fusionProId]) {
                        const productId = fusionProIdToProductIdMap[infoObj.fusionProId]
                        const pageId_js = productIdToPageIdMap[productId]
                        let url_js = ""
                        if (pageId_js) {
                            if (pageId_js === "9071114301890961" || pageId_js === "9021316215798968" || pageId_js === "9080115180683702") {
                                url_js = `https://jslt.zjhrnet.com/micropage-touliu/pages/otherMp/index?pageId=${pageId_js}&channelId=7&s=100000249&boothCode=JS-ZJHR4&boothAccessMode=31&_tlu=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&sourcePackage=${packageName}&unionSite=${unionSite}`;
                            } else {
                                url_js = `https://jslt.zjhrnet.com/micropage-touliu/pages/otherMp/index?pageId=${pageId_js}&channelId=7&s=100000249&boothCode=JS-ZJHR5&boothAccessMode=31&_tlu=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&sourcePackage=${packageName}&unionSite=${unionSite}`;
                            }
                            // else if(pageId_js === "9072013224829468") {
                            //     url_js = `https://qmlt.qiumo.net.cn/micropage-touliu/pages/otherMp/index?pageId=9072013224829468&channelId=11&s=100000292?from=hrtg001&_tlu=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&sourcePackage=${packageName}&unionSite=${unionSite}`;
                            // }
                            setTimeout(() => {
                                location.href = url_js
                            }, 500)
                        }
                        return
                    }

                    // 河北纳管跳转链接
                    // 河北联通-畅越优选云T包畅享版30元/月（河北）-立即生效-融合版
                    // 河北联通-畅越优选权益畅享短剧20元/月-纳管融合版
                    // 河北联通-19.9元8GB流量月包送中国石油权益-立即生效纳管融合版
                    // 河北联通-小书童29元/月6GB流量权益组合包（立即生效）
                    // 河北联通-畅越优选车主畅行包25元/月（河北）-立即生效
                    const productIdMap = {
                        '2c98ef5f9565a2a001956a399e250185': '2c98ef5f95125d0d019517ade58d0086',
                        '********************************': '2c98ef5f958d8944019593a2353f007e',
                        '2c98ef5f95d693f00195dc00746d009c': '2c98ef5f95d693f00195dbffd5910098',
                        '2c98ef5f95d693f00195da8dbcea0022': '2c98ef5f95c788990195d577afee010e',
                        '2c98ef5f961ec9fe0196234d69d30055': '2c98ef5f961ec9fe019623462bd60054',
                        '2c98ef5f96d7be990196e7ace61c0133': '2c98ef5f96d7be990196e7ac52d50132',
                        '********************************': '2c98ef5f9716bb29019719be02020016',
                    }
                    const urlTemplateMap = {
                        '2c98ef5f9565a2a001956a399e250185': 'https://hblt.shareinfo.com.cn/impgw_mall/ytbox/ytbox30/index',
                        '********************************': 'https://hblt.hebpolicycube.com/impgw_mall/duanju/y20/index',
                        '2c98ef5f95d693f00195dc00746d009c': 'https://hblt.hebpolicycube.com/impgw_mall/cyyx/cyyx19/index',
                        '2c98ef5f95d693f00195da8dbcea0022': 'https://hblt.hebpolicycube.com/impgw_mall/zjhr/xst29/index',
                        '2c98ef5f961ec9fe0196234d69d30055': 'https://hblt.shareinfo.com.cn/impgw_mall/cyyx/cyyx19/index?name=zjqm&from=hrtg001',
                        '2c98ef5f96d7be990196e7ace61c0133': 'https://hblt.hebpolicycube.com/impgw_mall/zjhr/qybox25_1/index',
                        '********************************': 'https://hblt.hebpolicycube.com/impgw_mall/zjhr/czcx25/index',
                    }
                    const generateRandomOrderId = () => {
                        let str = ''
                        for (let i = 0; i < 32; i++) {
                            str += Math.floor(Math.random() * 10)
                        }
                        return str
                    }
                    const buildUrl = (pid, phone, infoObj, c, clickId, a_oId, pageUrlId, name) => {
                        const productId = productIdMap[pid]
                        const urlTemplate = urlTemplateMap[pid]
                        const orderId = generateRandomOrderId()
                        if (productId === '2c98ef5f961ec9fe019623462bd60054') {
                            return `${urlTemplate}&utel=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&orderId=${orderId}&sourcePackage=${packageName}&unionSite=${unionSite}`
                        } else {
                            return `${urlTemplate}?utel=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&orderId=${orderId}&name=${name}&sourcePackage=${packageName}&unionSite=${unionSite}`
                        }
                    }
                    const handleProductRedirect = (p, phone, infoObj, c, clickId, a_oId, pageUrlId, name) => {
                        if (productIdMap[p] && urlTemplateMap[p]) {
                            const url_js = buildUrl(p, phone, infoObj, c, clickId, a_oId, pageUrlId, name)
                            setTimeout(() => {
                                location.href = url_js
                            }, 300);
                            return true
                        }
                        return false
                    };
                    // 河北联通纳管跳转逻辑
                    if (handleProductRedirect(p, phone, infoObj, c, clickId, a_oId, pageUrlId, 'zjhr')) {
                        return;
                    }

                    // 河北纳管跳转链接
                    // 河北移动-电费缴费权益包（20元电费版）
                    // 河北移动-20元12G流量月包
                    // 河北移动-随心看会员
                    const hebeiYidongProductIdMap = {
                        '2c98ef5f9666445a0196674d973f00fd': '2c98ef5f9666445a0196674cbeba00fb',
                        '2c98ef5f916eee1a01917978e7fe0131': '2c98ef5f916eee1a01917964aecc012c',
                        '2c98ef5f916eee1a0191797bf7f30135': '2c98ef5f916eee1a0191796c8f8f012f',
                    }
                    const hebeiYidongUrlTemplateMap = {
                        '2c98ef5f9666445a0196674d973f00fd': 'https://hbyd.zjhrnet.com/app/ecu/resource/cooperate/html/newIndex.html?priId=1C777D4C4C3249CDA15A8E9918FD7D76&vrStr=5420606D0C7AC9BC9A53EC5C6274EF11',
                        '2c98ef5f916eee1a01917978e7fe0131': 'https://hbyd.zjhrnet.com/app/ecu/resource/cooperate/html/newIndex.html?priId=D555CA12207343E4BB86682127CE78FB&vrStr=5420606D0C7AC9BC9A53EC5C6274EF11',
                        '2c98ef5f916eee1a0191797bf7f30135': 'https://hbyd.zjhrnet.com//app/ecu/resource/cooperate/html/newIndex.html?priId=FA1D55B8453641C082BC890D8C90A42A&vrStr=5420606D0C7AC9BC9A53EC5C6274EF11',
                    }
                    const buildHebeiYidongUrl = (pid, phone, infoObj, c, clickId, a_oId, pageUrlId) => {
                        const productId = hebeiYidongProductIdMap[pid]
                        const urlTemplate = hebeiYidongUrlTemplateMap[pid]
                        const orderId = generateRandomOrderId()
                        return `${urlTemplate}&mobile=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}&orderId=${orderId}&sourcePackage=${packageName}&unionSite=${unionSite}`
                    }
                    const handleHebeiYidongRedirect = (p, phone, infoObj, c, clickId, a_oId, pageUrlId) => {
                        if (hebeiYidongProductIdMap[p] && hebeiYidongUrlTemplateMap[p]) {
                            const url_js = buildHebeiYidongUrl(p, phone, infoObj, c, clickId, a_oId, pageUrlId)
                            setTimeout(() => {
                                location.href = url_js
                            }, 300);
                            return true
                        }
                        return false
                    };
                    // 河北移动纳管跳转逻辑
                    if (handleHebeiYidongRedirect(p, phone, infoObj, c, clickId, a_oId, pageUrlId)) {
                        return;
                    }


                    // location.href = `https://jslt.zjhrnet.com/micropage-touliu/pages/otherMp/index?pageId=9092517452816395&channelId=7&s=100000249&boothCode=JS-ZJHR5&boothAccessMode=31&_tlu=${phone}&p=${productId}&fusionPid=${infoObj.fusionProId}&c=${c}&clickid=${clickId}&a_oId=${a_oId}&pageid=${pageUrlId}&sourcePageId=${pageUrlId}`;
                    $('title').html(`${infoObj.title}`)
                    var linkElement = $('link[rel="shortcut icon"]:last');
                    linkElement.attr('href', `${infoObj.icon}`)
                    // // $(".top-img img").attr("src", infoObj.headImgUrl)
                    // $('.top-img').html(`<img src="${infoObj.headImgUrl}">`)
                    if (infoObj.headImgUrl) {
                        $(".top-img img").attr("src", infoObj.headImgUrl)
                    }
                    //  倒计时处理
                    if (infoObj.countdown) {
                        $('.form-scrolls').show()
                    } else {
                        $('.form-scrolls').hide()
                    }
                    // 咪咕数媒页面处理(非优化版-破解)
                    // 咪咕数媒-咪咕悦读畅享会员9.9元-融合版  咪咕数媒-咪咕短剧超级会员20元-融合版  网易云-游戏定向流量会员月包20元12GB-融合版
                    if (p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002' || p === '2c98ef5f96ed10b00196fc037d9601c5') {
                        $('.form-item-code').hide()
                    } else if (p === '2c98ef5f95eef6700195ef461fb60023' || p === '2c98ef5f95eef6700195ef4568560022') {
                        // 悦享铂金VIP-融合版  尊享铂金VIP-融合版
                        const script = document.createElement('script')
                        script.src = 'https://account.bol.wo.cn/static/js-sdk/wolog/WoAnalytics.js'
                        script.onload = () => {
                            console.log('WoAnalytics.js 已加载完成')
                            //中心打点_open
                            WoAnalytics.addPoint({
                                ad: '070f2d9b0319468ab73073842296b9ed',
                                at: 'activity' + pageid,
                                ett: 'open',
                                ea: 'activity_detail_view',
                                cpn: 'activity_detail'
                            })

                        }
                        document.head.appendChild(script)

                        // 添加新的 script 标签  插码逻辑
                        const newScript = document.createElement('script');
                        newScript.src = 'https://account.bol.wo.cn/static/js-sdk/wolog/WoAnalytics.js';
                        newScript.async = true;
                        newScript.onload = () => {
                            console.log('新脚本已加载完成');

                            // 可以在这里调用新脚本提供的 API 或初始化逻辑
                            function generateUUID() {
                                let uuid = '', i, random;
                                for (i = 0; i < 32; i++) {
                                    random = Math.random() * 16 | 0;
                                    uuid += (i === 12 ? 4 : (i === 16 ? (random & 3 | 8) : random)).toString(16);
                                }
                                return uuid;
                            }

                            sanfang_uuid = generateUUID()
                            console.log(generateUUID());
                            const sanfangGenerateRandomOrderId = () => {
                                let str = ''
                                for (let i = 0; i < 32; i++) {
                                    str += Math.floor(Math.random() * 10)
                                }
                                return str
                            }

                            function pageDataFunction(mobile, event_type, treesid) {
                                if (event_type == 2) {
                                    sanfang_orderId = sanfangGenerateRandomOrderId()
                                }
                                const product_id = ''
                                if (p === '2c98ef5f95eef6700195ef461fb60023') {
                                    product_id = '11084'
                                }
                                if (p === '2c98ef5f95eef6700195ef4568560022') {
                                    product_id = '11249'
                                }

                                let query = {
                                    source: 105,  //  平台  一个平台对应一个密钥
                                    key: 'LuJ339l@z5336Kc8',  // 密钥  一个平台对应一个密钥
                                    type: 1,  //   1套餐 2资源
                                    productid: product_id,  // 产品id 套餐id 类型 1套餐 2资源
                                    event_type,  // 事件类型 1:打开页面、2:点击订购、3:获取验证码（cap页面）、4:输入验证码(cap页面)、5:点击订购确认(cap页面)、6: 订购成功、 7:退出页面
                                    mobile,   // 手机号
                                    order_id: orderId,   // 订单id
                                    treesid,   // 唯一标识
                                    channelid: '15796709', // 渠道id
                                    paytype: 8   //  支付方式 1 话费 6支付宝 8 微信 7阅点
                                }
                                readOrderTrack.set(query);
                            }

                            pageDataFunction(phone, 1, sanfang_uuid)
                        };
                        document.head.appendChild(newScript);
                        $('.form-item-code').hide()
                        window.addEventListener('userIpReady', function (event) {
                            console.log(event.detail) // 在这里使用 userIp
                            userIp = event.detail
                        })
                    } else {
                        $('.form-item-code').show()
                    }
                    if (autoInfo.guidelines) {
                        $('.form_img_1').hide()
                        $('.form_img_2').show()
                    }
                    if (infoObj.backgroundColor) {
                        $('body').css('backgroundColor', `${infoObj.backgroundColor}`)
                    } else {
                        $('body').css('backgroundColor', '#e43b4e')
                    }
                    const $formBtn = $('.form-btn')
                    $formBtn.css({
                        background: `url(${infoObj.buttonBackImgUrl || 'https://oss.zjhrnet.com/img/cunliang/liangtong/duosheng/toutiao/zj-t2-new.png'}) no-repeat`,
                        width: "6.8rem",
                        height: "2rem",
                        backgroundSize: "contain",
                        backgroundPosition: "center",
                    })
                    if (infoObj.tariffDescription) {
                        if (p === '2c98ef5f96142f9f0196199080d200b3' || p === '2c98ef5f961ec9fe01961eee1c450009') {
                            $(".form-text").html(`<a href="tel:400-1050299" style="color:#fff">客服电话:400-1050299</a>`)
                        } else {
                            $(".form-text").html(infoObj.tariffDescription)
                        }
                    } else {
                        $('.form-text').empty()
                    }
                    if (infoObj.proImgUrl) {
                        $('.pro-img').empty().append(
                            infoObj.proImgUrl.split(',')
                                .map(imgUrl => `<img src="${imgUrl}">`)
                                .join('')
                        );
                    } else {
                        $('.pro-img').empty()
                    }
                    // $('.tips-text').html(infoObj.proContent)
                    // 落单页活动说明长度小于20隐藏
                    if (infoObj.proContent) {
                        // 使用正则表达式匹配中文字符
                        const chineseCharCount = (infoObj.proContent.match(/[\u4e00-\u9fa5]/g) || []).length;
                        if (infoObj.proContent.includes('活动说明') && chineseCharCount < 20) {
                            $('.rule-title').hide()
                            $('.title_img').hide()
                        } else {
                            $('.rule-title').show()
                            $('.title_img').show()
                            $('.tips-text').html(infoObj.proContent)
                        }
                    } else {
                        $('.tips-text').empty()
                    }

                    if (infoObj.authImgUrl) {
                        $('.sq-img').html(`<img src="${infoObj.authImgUrl}">`)
                    }
                    if (infoObj.showAgreement) {
                        $('.form-ys').show()
                    } else {
                        $('.form-ys').hide()
                    }
                    if (infoObj.guidelines === 1) {
                        // $('.form_img_1').show()
                        if (infoObj.oneGuidelinesUrl) {
                            $(".form_img_1").attr("src", infoObj.oneGuidelinesUrl)
                        } else {
                            $(".form_img_1").attr("src", 'https://cl-autopage-material.oss-cn-hangzhou.aliyuncs.com/cl-autopage-material/1729821001542.gif')
                        }
                        if (infoObj.twoGuidelinesUrl) {
                            $(".form_img_2").attr("src", infoObj.twoGuidelinesUrl)
                        }
                        if (infoObj.threeGuidelinesUrl) {
                            $(".form_img_3").attr("src", infoObj.threeGuidelinesUrl)
                        } else {
                            $(".form_img_3").attr("src", 'https://cl-autopage-material.oss-cn-hangzhou.aliyuncs.com/cl-autopage-material/1729821642287.gif')
                        }
                        if (infoObj.fourGuidelinesUrl) {
                            $(".form_img_4").attr("src", infoObj.fourGuidelinesUrl)
                        }
                    } else {
                        $('.form_img_1').hide()
                        $('.form_img_2').hide()
                        $('.form_img_3').hide()
                        $('.form_img_4').hide()
                    }

                    // 广点通特殊处理
                    flag = false
                }
                //判定手机号码输入完整
                $("#phone").bind("input propertychange", function (e) {
                    let lengt = $("#phone").val().length;
                    phone = $("#phone").val();
                    if (lengt >= 1) {
                        if (lengt == 11) {
                            if (!isPhone($("#phone").val())) {
                                message.info("请输入正确的手机号码");
                                return;
                            } else {
                                console.log("正确，跳转");
                                if (autoInfo.type === 1) {
                                    // 广东移动审核页插码
                                    if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                                        || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                                        || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                                        || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                                        || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                                        || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                                        || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                                        || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                                        || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                                        || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                                    ) {
                                        const productConfig = getMerchantsInfo(p);
                                        console.log(productConfig);
                                        sessionStorage.setItem('gdydpId', p)
                                        sessionStorage.setItem('gdydHost', productConfig.domain)
                                        loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangdong/gdp-full.js')
                                            .then(() => {
                                                console.log('广东移动插码 已加载完成');
                                                // 初始化gdp
                                                gdp('init', '8d2279a5e2f18b7c', '9d59ac736f01c688', {
                                                    host: 'collect.gmcc.net',
                                                    compress: false,
                                                    hashtag: true,
                                                    debug: true
                                                });
                                                gdp('setGeneralProps', {
                                                    "global_merchantsId_var": productConfig.merchantsId,
                                                    "global_merchants_var": productConfig.merchantsName,
                                                    "global_apiId_var": "smscodechkodcommitorder",
                                                    "global_apiName_var": "短信验证及订单提交",
                                                    "global_merchantsIdCity_var": "省统"
                                                });

                                                // 先加载crypto-js
                                                return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                                            })
                                            .then(() => {
                                                console.log('crypto-js 加载完成');
                                                // 加载配置文件
                                                return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangDongInsertCodeConfig.js');
                                            })
                                            .then(() => {
                                                console.log('广东移动插码配置 已加载完成');
                                                window.gdGxInsertCodeReady = true;
                                                event_upload("页面浏览", null, null, encryptAES(phone), null, null, null, p);
                                                event_upload("输入电话号码", null, null, encryptAES(phone), null, null, null, p);
                                            })
                                            .catch(error => {
                                                console.error('脚本加载失败:', error);
                                                window.gdGxInsertCodeReady = true;
                                            });
                                    } else if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                                        loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangxi/gdp-full.js')
                                            .then(() => {
                                                console.log('广西移动插码 已加载完成');
                                                // 广西移动插码初始化
                                                gdp('init', '86e27c5fd2e5e4cd', '8bcdeab9a38bdeef', {
                                                    serverUrl: 'www.gx.10086.cn',// 数据发送地址。
                                                    compress: false,// 开启数据压缩模式。
                                                    hashtag: true,// 开启hash解析
                                                    scheme: 'https',
                                                    debug: true
                                                });
                                                // 广西移动全局变量采集
                                                gdp('setGeneralProps', {
                                                    "XY_global_merchantsId": "21874", // 店铺编号
                                                    "XY_global_merchants": "中网数科（成都）科技有限公司", // 店铺名称
                                                    "XY_global_apiName": "流量，套餐业务办理接口", // 调用接口名称；传产品最后受理的接口名称
                                                    "XY_global_apiUrl": "https://cardstatic.zjhrnet.com/cardfr/clcommonapi/createOrder" // 生产环境调用接口地址；传产品最后受理的调用接口地址
                                                });

                                                // 先加载crypto-js
                                                return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                                            })
                                            .then(() => {
                                                console.log('crypto-js 加载完成');
                                                // 加载配置文件
                                                return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangXiInsertCodeConfig.js');
                                            })
                                            .then(() => {
                                                console.log('广西移动插码配置 已加载完成');
                                                window.gdGxInsertCodeReady = true;
                                                guangXi_event_upload(type = "页面浏览", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                                guangXi_event_upload(type = "输入电话号码", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                            })
                                            .catch(error => {
                                                console.error('脚本加载失败:', error);
                                                window.gdGxInsertCodeReady = true;
                                            });
                                    } else {
                                        window.gdGxInsertCodeReady = true;
                                    }

                                    pageData_auto(c, phone, p, 2, 1)
                                    if (autoInfo.guidelines) {
                                        $('.form_img_1').hide()
                                        $('.form_img_2').show()
                                    } else {
                                        $('.form_img_1').hide()
                                        $('.form_img_2').hide()
                                    }
                                    if (autoInfo.sendCode === 1) {
                                        mg_checkPhone()
                                    }
                                }
                                if (autoInfo.type === 2) {
                                    // 检查包名
                                    if (url.includes('c623f8f1b4c3416488bdfe0e730d1191')) {
                                        autoLinkResult()
                                    } else if (url.includes('ad17df9ec5b04db5a558b9d23f0ef5ae') || url.includes('d456d49b735946e387d849a391e07307')) {
                                        // 广点通链接显示弹窗
                                        $('.show_windws_text_2').show()
                                    } else {
                                        if (packageName) {
                                            checkPackage(c, p, packageName)
                                                .then(res => {
                                                    console.log(res.status)
                                                    if (res.status === 200 && res.payload) {
                                                        autoLinkResult()
                                                    } else {
                                                        getAd('order_error', 2, 2)
                                                    }
                                                })
                                                .catch(err => {
                                                    getAd('order_error', 2, 2)
                                                })
                                        } else {
                                            autoLinkResult()
                                        }
                                    }
                                }
                                // 号码过一阶段
                                if (phone == '13058180000') {
                                    touNewApi(clickId, p, c, 'success')
                                }
                            }
                        } else {
                            if (autoInfo.guidelines) {
                                $('.form_img_1').show()
                                $('.form_img_2').hide()
                                $('.form_img_3').hide()
                            } else {
                                $('.form_img_1').hide()
                                $('.form_img_2').hide()
                                $('.form_img_3').hide()
                            }
                        }
                    } else {
                        if (autoInfo.guidelines) {
                            $(".form_img_1").show()
                            $(".form_img_2").hide()
                            $(".form_img_3").hide()
                        } else {
                            $(".form_img_1").hide()
                            $(".form_img_2").hide()
                            $(".form_img_3").hide()
                        }
                    }
                });
                //--//
                //弹窗提示--//
                function showWindwsText(showtext) {
                    // $(".show_windws_text .d").html(showtext);
                    // $(".show_windws_text").show();
                    // $(".bg").show();
                    message.info(`${showtext}`)
                }

                $(".show_windws_text .butt .b").on("click", function () {
                    $(".show_windws_text").hide();
                    $(".bg").hide();
                });

                $('.form-ys-radio').click(function () {
                    $(this).toggleClass('form-ys-radio-active');
                })
                //--//
                //获取验证码
                $(".form-code").click(function () {

                    phone = $("#phone").val();
                    // // 广东联通插码逻辑
                    // if (c==='7de10f7e9e754ca78b22330e11c22113'){
                    //     const gdlt_iframe = $('.ah-jscode')[0];
                    //     if (gdlt_iframe && gdlt_iframe.contentWindow) {
                    //         gdlt_iframe.contentWindow.postMessage(
                    //             { type: 'CODE', data: '1998910' },
                    //             window.location.origin
                    //         );
                    //     } else {
                    //         console.error('gdlt_iframe 或 contentWindow 未正确加载');
                    //     }
                    // }
                    // 秋末电信js插码
                    // 读赞-重庆电信-15.9元视频彩铃+视听权益包-融合版
                    // 读赞-重庆电信-19视听会员5G流量包-融合版
                    // 读赞-电信-尊享会员（全国）-融合版
                    if ((p === '2c98ef5f958d8944019593fe2da9008c'
                        || p === '2c98ef5f96142f9f0196198fd96e00b2'
                        || p === '2c98ef5f96b384ba0196b413380b0099'
                        || p === '2c98ef5f96b384ba0196b408d9020083'
                        || p === '2c98ef5f96b384ba0196b3f90fe30045') && !voucherNumber) {
                        AgentControler.checkPhoneAndCheck(phone);
                        AgentControler.checkVoucherNumber().then(res => {
                            console.log(res);// 流水号（发送验证码和下单时作为入参voucherNumber，下单校验需要）
                            console.log(res.voucherNumber);
                            voucherNumber = res.voucherNumber
                        }).catch(err => {
                            console.log(err);// 错误信息
                        })
                    }
                    if (autoInfo.type === 1 && autoInfo.showAgreement) {
                        if (autoInfo.checkAgreement) {
                            $('.form-ys-radio').addClass('form-ys-radio-active');
                        } else {
                            if (!$('.form-ys-radio').hasClass('form-ys-radio-active')) {
                                showWindwsText("请阅读并勾选协议！");
                                return;
                            }
                        }
                    }
                    if (autoInfo.type === 2 && infoObj.showAgreement) {
                        if (infoObj.checkAgreement) {
                            $('.form-ys-radio').addClass('form-ys-radio-active');
                        } else {
                            if (!$('.form-ys-radio').hasClass('form-ys-radio-active')) {
                                showWindwsText("请阅读并勾选协议！");
                                return;
                            }
                        }
                    }
                    if (!phone) {
                        showWindwsText("请输入办理的手机号码");
                        return;
                    }
                    if (!isPhone(phone)) {
                        showWindwsText("请输入正确的手机号码");
                        return;
                    }
                    if (count > 0) {
                        return;
                    }
                    if (isGetCode) {
                        mg_checkPhone();
                        return;
                    }
                    if (url.includes('page=a9ffa39e0c274a879666d82cda29e66d') || url.includes('page=4d64b1beb2a3463497d90f699108cfa5')) {
                        sth_ag.sendSmsFront({
                            phoneNumber: phone
                        }).then(res => {
                            console.log(res);
                            if (res.resultCode == '0000') {
                                console.log('短信验证码发送前置处理成功，uuid1=' + res.data.uuid1);
                                console.log(res.data.uuid1);
                                gzlt_uuid1 = res.data.uuid1
                                mg_checkPhone();
                            } else {
                                console.log('短信验证码发送前置处理失败，失败原因：' + res.resultMsg);
                            }
                        });
                    } else {
                        mg_checkPhone()
                    }
                });
                //获取验证码
                const mg_checkPhone = function (specialCmccItem) {
                    if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                        || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                        || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                        || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                        || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                        || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                        || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                        || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                        || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                        || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                    ) {
                        const productConfig = getMerchantsInfo(p);
                        console.log(productConfig);
                        sessionStorage.setItem('gdydpId', p)
                        sessionStorage.setItem('gdydHost', productConfig.domain)
                        // 广东移动插码初始化
                        loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangdong/gdp-full.js')
                            .then(() => {
                                console.log('广东移动插码 已加载完成');
                                // 初始化gdp
                                gdp('init', '8d2279a5e2f18b7c', '9d59ac736f01c688', {
                                    host: 'collect.gmcc.net',
                                    compress: false,
                                    hashtag: true,
                                    debug: true
                                });
                                gdp('setGeneralProps', {
                                    "global_merchantsId_var": productConfig.merchantsId,
                                    "global_merchants_var": productConfig.merchantsName,
                                    "global_apiId_var": "smscodechkodcommitorder",
                                    "global_apiName_var": "短信验证及订单提交",
                                    "global_merchantsIdCity_var": "省统"
                                });

                                // 先加载crypto-js
                                return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                            })
                            .then(() => {
                                console.log('crypto-js 加载完成');
                                // 加载配置文件
                                return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangDongInsertCodeConfig.js');
                            })
                            .then(() => {
                                console.log('广东移动插码配置 已加载完成');
                                window.gdGxInsertCodeReady = true;
                                event_upload("页面浏览", null, null, encryptAES(phone), null, null, null, p);
                                event_upload("输入电话号码", null, null, encryptAES(phone), null, null, null, p);
                            })
                            .catch(error => {
                                console.error('脚本加载失败:', error);
                                window.gdGxInsertCodeReady = true;
                            });
                    }

                    if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                        sessionStorage.setItem('gxydpId', p)
                        loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangxi/gdp-full.js')
                            .then(() => {
                                console.log('广西移动插码 已加载完成');
                                // 广西移动插码初始化
                                gdp('init', '86e27c5fd2e5e4cd', '8bcdeab9a38bdeef', {
                                    serverUrl: 'www.gx.10086.cn',// 数据发送地址。
                                    compress: false,// 开启数据压缩模式。
                                    hashtag: true,// 开启hash解析
                                    scheme: 'https',
                                    debug: true
                                });
                                // 广西移动全局变量采集
                                gdp('setGeneralProps', {
                                    "XY_global_merchantsId": "21874", // 店铺编号
                                    "XY_global_merchants": "中网数科（成都）科技有限公司", // 店铺名称
                                    "XY_global_apiName": "流量，套餐业务办理接口", // 调用接口名称；传产品最后受理的接口名称
                                    "XY_global_apiUrl": "https://cardstatic.zjhrnet.com/cardfr/clcommonapi/createOrder" // 生产环境调用接口地址；传产品最后受理的调用接口地址
                                });

                                // 先加载crypto-js
                                return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                            })
                            .then(() => {
                                console.log('crypto-js 加载完成');
                                // 加载配置文件
                                return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangXiInsertCodeConfig.js');
                            })
                            .then(() => {
                                console.log('广西移动插码配置 已加载完成');
                                window.gdGxInsertCodeReady = true;
                                guangXi_event_upload(type = "页面浏览", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                guangXi_event_upload(type = "输入电话号码", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                            })
                            .catch(error => {
                                console.error('脚本加载失败:', error);
                                window.gdGxInsertCodeReady = true;
                            });
                    }

                    // 广东移动插码等待逻辑
                    if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                        || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                        || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                        || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                        || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                        || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                        || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                        || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                        || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                        || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                        // 广西移动产品
                        || p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                        if (!window.gdGxInsertCodeReady) {
                            setTimeout(() => {
                                mg_checkPhone();
                            }, 50);
                            return;
                        }
                    }
                    // 广西-百妙骏业验证码下发字段
                    let currentPageUrl = null
                    if (p === '2c98ef5f983b803a01983bd8e7fa010e') {
                        currentPageUrl = window.location.href
                    }
                    // 安徽联通插码拿不到token处理逻辑
                    if (p === '2c98ef5f916eee1a01917dbe1d440226' && !ahLoginToken) {
                        message.info("请刷新页面重试");
                        return
                    }
                    $(".load_bg").show();
                    let data = JSON.stringify({
                        pid: p,
                        acid: c,
                        mobile: phone,
                        entranName: packageName,
                        ahLoginToken: ahLoginToken,
                        // 秋末电信js插码字段
                        voucherNumber: voucherNumber,
                        // 贵州联通js插码字段
                        gzltUuid1: gzlt_uuid1,
                        // 广西-百妙骏业验证码下发字段
                        pageUrl: currentPageUrl
                    });
                    pageData_auto(c, phone, p, 3,);
                    // 广东移动插码
                    if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                        || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                        || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                        || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                        || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                        || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                        || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                        || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                        || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                        || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                    ) {
                        event_upload("获取短信验证码", "smscodeapply", apiName = "短信验证码申请", aesPhone = encryptAES(phone), null, null, null, pid = p);
                    }
                    // 广西移动插码
                    if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                        guangXi_event_upload(type = "获取短信验证码", pId = p, apiName = '业务短信下发接口', apiUrl = 'https://cardstatic.zjhrnet.com/cardfr/clcommonapi/sendSms', aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                    }
                    // 辽宁移动插码
                    if (p === '2c98ef5f94170fed01941a58e4bf002f' || p === '2c98ef5f94170fed01941a57bac2002e' || p === '2c98ef5f947d3d98019482758a700028' || p === '2c98ef5f94b1cf770194d504518702fa') {
                        lnlt_jscode(3, phone, p)
                    }
                    GmAjax(
                        "POST",
                        "clcommonapi/sendSms",
                        "application/json",
                        data,
                        "json",
                        function (res) {
                            console.log(res);
                            if (res.status == 200 || res.status == 201) {
                                pageData_auto(c, phone, p, 4,)
                                // 特殊链接移动电信号码直接下发验证码 2025-02-07
                                // if ((c === 'e4f671bcc3554c2db70ec63e7884d3f9' || c === '20a38e61ba3b49a9a915d068d3c1ebf5' || c === '67a0dfd156154dbcbbfb1661a952ee28' || c === '0c7724f7bcfd44a4922a949aceb866cc' || c === '08d2872950b84287ad0042db9ed152f6' || c === '9cecd8b3ab574065bfdeeda3d0692c2b' || c === 'a0b67cac2d714d08901dc9e133187d05') && (autoLinkInfo.cmcc === '移动' || autoLinkInfo.cmcc === '电信') && specialCmccItem) {
                                //     updateUI(specialCmccItem)
                                // }
                                if (autoInfo.type === 2) {
                                    if ((autoLinkInfo.cmcc === '移动' || autoLinkInfo.cmcc === '电信') && specialCmccItem) {
                                        updateUI(specialCmccItem, 'specialShow')
                                    } else {
                                        updateUI(infoObj, 'specialShow')
                                    }
                                }
                                if (!infoObj.recommend) {
                                    pageData_auto(c, phone, p, 11, 2)
                                }
                                count = 60;
                                $(".form-code").text(count);
                                isGetCode = true;
                                time = setInterval(function () {
                                    count--;
                                    $(".form-code").text(count);
                                    if (count <= 0) {
                                        $(".form-code").text("重新获取");
                                        clearInterval(time);
                                    }
                                }, 1000);
                                $(".load_bg").hide();
                                smsPhone = $("#phone").val();
                                reqSn = res.payload.reqSn;
                                telcoOrderNo = res.payload.telcoOrderNo;
                                sendSms_payload = res.payload;
                                if (autoInfo.guidelines) {
                                    $(".form_img_3").show();
                                    $(".form_img_2").hide();
                                }
                                message.info("发送成功");
                            } else {
                                pageData_auto(c, phone, p, 5,);
                                $(".load_bg,.loading").hide();
                                if (autoInfo.type === 1) {
                                    message.info(`${res.error}`);
                                }
                                if (autoInfo.type === 2) {
                                    // message.info(`${res.error}`);
                                    // getAd('send_error', 1)
                                    // 下发验证码失败推荐下一个产品
                                    // 特殊链接移动电信号码直接下发验证码 2025-02-07
                                    // if (productList.length > 1 && !infoObj.recommend) {
                                    //     for (let index = 0; index < productList.length; index++) {
                                    //         const item = productList[index];
                                    //         if (p === item.fusionProId) {
                                    //             const nextItem = productList[index + 1];
                                    //             if (nextItem) {
                                    //                 pageData_auto(c, phone, p, 2)
                                    //                 updateUI(nextItem);
                                    //                 return
                                    //             } else {
                                    //                 getAd('send_error', 1)
                                    //             }
                                    //         }
                                    //     }
                                    // } else {
                                    //     getAd('send_error', 1)
                                    // }
                                    // if ((productList.length > 1 && !infoObj.recommend) || (productList.length > 1 && infoObj.recommend && autoLinkInfo.cmcc === '移动') || (productList.length > 1 && res.error.includes('请勿重复'))) {
                                    if ((productList.length > 1 && !infoObj.recommend) || (productList.length > 1 && infoObj.recommend && (autoLinkInfo.cmcc === '移动' || autoLinkInfo.cmcc === '电信'))) {
                                        for (let index = 0; index < productList.length; index++) {
                                            const item = productList[index];
                                            if (p === item.fusionProId) {
                                                let nextIndex = index + 1;
                                                let nextItem = productList[nextIndex];
                                                // 新增逻辑：如果nextItem存在且sendCode为0，则继续往后找
                                                while (nextItem && nextItem.sendCode === 0) {
                                                    nextIndex++;
                                                    nextItem = productList[nextIndex];
                                                }
                                                if (nextItem) {
                                                    infoObj = nextItem
                                                    p = nextItem.fusionProId
                                                    const checkResult = checkAndCloseSpecialProduct(infoObj, autoLinkInfo, packageName);
                                                    console.log(checkResult, '------------------------------');
                                                    if (checkResult) {
                                                        if (nextItem.telcoType === "7") {
                                                            console.log("三方支付")
                                                        } else {
                                                            pageData_auto(c, phone, p, 2, 0);
                                                        }
                                                        getAd('order_error', 2, 1);
                                                        return;
                                                    }
                                                    if (nextItem.telcoType === "7") {
                                                        console.log("三方支付")
                                                    } else {
                                                        pageData_auto(c, phone, p, 2, 1)
                                                    }
                                                    // // 特定账户移动、电信号码下发验证码失败直接下发可办理下一个产品
                                                    // if ((c === 'e4f671bcc3554c2db70ec63e7884d3f9' || c === '20a38e61ba3b49a9a915d068d3c1ebf5' || c === '67a0dfd156154dbcbbfb1661a952ee28' || c === '0c7724f7bcfd44a4922a949aceb866cc' || c === '08d2872950b84287ad0042db9ed152f6' || c === '9cecd8b3ab574065bfdeeda3d0692c2b' || c === 'a0b67cac2d714d08901dc9e133187d05' || c === '7a2259d7073a495dba653ce99acf94e7' || c === '314b6e4e94824c53ad22f0a502707c45' || c === 'b777fb2f8c2744f6bc434f93a0c1c19f') && (autoLinkInfo.cmcc === '移动' || autoLinkInfo.cmcc === '电信')) {
                                                    //     mg_checkPhone(nextItem)
                                                    // } else {
                                                    //     updateUI(nextItem);
                                                    // }
                                                    // 新增：如果p为河北移动纳管产品，直接走updateUI逻辑
                                                    if (p === '2c98ef5f9666445a0196674d973f00fd'
                                                        || p === '2c98ef5f916eee1a01917978e7fe0131'
                                                        || p === '2c98ef5f916eee1a0191797bf7f30135') {
                                                        updateUI(nextItem);
                                                    } else {
                                                        if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                                                            || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                                                            || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                                                            || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                                                            || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                                                            || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                                                            || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                                                            || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                                                            || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                                                            || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                                                        ) {
                                                            const productConfig = getMerchantsInfo(p);
                                                            console.log(productConfig);
                                                            sessionStorage.setItem('gdydpId', p)
                                                            sessionStorage.setItem('gdydHost', productConfig.domain)
                                                            loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangdong/gdp-full.js')
                                                                .then(() => {
                                                                    console.log('广东移动插码 已加载完成');
                                                                    // 初始化gdp
                                                                    gdp('init', '8d2279a5e2f18b7c', '9d59ac736f01c688', {
                                                                        host: 'collect.gmcc.net',
                                                                        compress: false,
                                                                        hashtag: true,
                                                                        debug: true
                                                                    });
                                                                    gdp('setGeneralProps', {
                                                                        "global_merchantsId_var": productConfig.merchantsId,
                                                                        "global_merchants_var": productConfig.merchantsName,
                                                                        "global_apiId_var": "smscodechkodcommitorder",
                                                                        "global_apiName_var": "短信验证及订单提交",
                                                                        "global_merchantsIdCity_var": "省统"
                                                                    });

                                                                    // 先加载crypto-js
                                                                    return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                                                                })
                                                                .then(() => {
                                                                    console.log('crypto-js 加载完成');
                                                                    // 加载配置文件
                                                                    return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangDongInsertCodeConfig.js');
                                                                })
                                                                .then(() => {
                                                                    console.log('广东移动插码配置 已加载完成');
                                                                    window.gdGxInsertCodeReady = true;
                                                                    event_upload("页面浏览", null, null, encryptAES(phone), null, null, null, p);
                                                                    event_upload("输入电话号码", null, null, encryptAES(phone), null, null, null, p);
                                                                    setTimeout(() => {
                                                                        mg_checkPhone(nextItem)
                                                                    }, 300)
                                                                })
                                                                .catch(error => {
                                                                    console.error('脚本加载失败:', error);
                                                                    window.gdGxInsertCodeReady = true;
                                                                });
                                                            return;
                                                        }
                                                        if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                                                            sessionStorage.setItem('gxydpId', p)
                                                            loadScript('https://cardstatic.zjhrnet.com/front/utils/insertCodeJs/guangxi/gdp-full.js')
                                                                .then(() => {
                                                                    console.log('广西移动插码 已加载完成');
                                                                    // 广西移动插码初始化
                                                                    gdp('init', '86e27c5fd2e5e4cd', '8bcdeab9a38bdeef', {
                                                                        serverUrl: 'www.gx.10086.cn',// 数据发送地址。
                                                                        compress: false,// 开启数据压缩模式。
                                                                        hashtag: true,// 开启hash解析
                                                                        scheme: 'https',
                                                                        debug: true
                                                                    });
                                                                    // 广西移动全局变量采集
                                                                    gdp('setGeneralProps', {
                                                                        "XY_global_merchantsId": "21874", // 店铺编号
                                                                        "XY_global_merchants": "中网数科（成都）科技有限公司", // 店铺名称
                                                                        "XY_global_apiName": "流量，套餐业务办理接口", // 调用接口名称；传产品最后受理的接口名称
                                                                        "XY_global_apiUrl": "https://cardstatic.zjhrnet.com/cardfr/clcommonapi/createOrder" // 生产环境调用接口地址；传产品最后受理的调用接口地址
                                                                    });

                                                                    // 先加载crypto-js
                                                                    return loadScript('https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js');
                                                                })
                                                                .then(() => {
                                                                    console.log('crypto-js 加载完成');
                                                                    // 加载配置文件
                                                                    return loadScript('https://cardstatic.zjhrnet.com/front/utils/public/js/guangXiInsertCodeConfig.js');
                                                                })
                                                                .then(() => {
                                                                    console.log('广西移动插码配置 已加载完成');
                                                                    window.gdGxInsertCodeReady = true;
                                                                    guangXi_event_upload(type = "页面浏览", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                                                    guangXi_event_upload(type = "输入电话号码", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                                                                    setTimeout(() => {
                                                                        mg_checkPhone(nextItem)
                                                                    }, 300)
                                                                })
                                                                .catch(error => {
                                                                    console.error('脚本加载失败:', error);
                                                                    window.gdGxInsertCodeReady = true;
                                                                });
                                                            return;
                                                        }

                                                        mg_checkPhone(nextItem)
                                                    }
                                                    return
                                                } else {
                                                    message.info(`${res.error}`);
                                                    getAd('send_error', 1, 1)
                                                }
                                            }
                                        }
                                    } else {
                                        message.info(`${res.error}`);
                                        getAd('send_error', 1, 1)
                                    }
                                }
                            }
                        }
                    );
                };
                //下单按钮
                $(".form-btn,.click-btn").click(function () {
                    phone = $("#phone").val();
                    code = $("#code").val();
                    if (autoInfo.type === 1 && autoInfo.showAgreement) {
                        if (autoInfo.checkAgreement) {
                            $('.form-ys-radio').addClass('form-ys-radio-active');
                        } else {
                            if (!$('.form-ys-radio').hasClass('form-ys-radio-active')) {
                                showWindwsText("请阅读并勾选协议！");
                                return;
                            }
                        }
                    }
                    if (autoInfo.type === 2 && infoObj.showAgreement) {
                        if (infoObj.checkAgreement) {
                            $('.form-ys-radio').addClass('form-ys-radio-active');
                        } else {
                            if (!$('.form-ys-radio').hasClass('form-ys-radio-active')) {
                                showWindwsText("请阅读并勾选协议！");
                                return;
                            }
                        }
                    }
                    // 咪咕数媒统一认证模式逻辑
                    if (p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002') {
                        if (!phone) {
                            showWindwsText("请输入办理的手机号码");
                            return;
                        }
                        if (!isPhone(phone)) {
                            showWindwsText("请输入正确的手机号码");
                            return;
                        }
                        mg_createOrderMiGu()
                        return
                    }
                    // 网易云游戏定向包统一认证模式逻辑
                    if (p === '2c98ef5f96ed10b00196fc037d9601c5') {
                        if (!phone) {
                            showWindwsText("请输入办理的手机号码");
                            return;
                        }
                        if (!isPhone(phone)) {
                            showWindwsText("请输入正确的手机号码");
                            return;
                        }
                        mg_createOrderWangYiYun()
                        return
                    }
                    // 三方支付统一认证逻辑
                    if (p === '2c98ef5f95eef6700195ef461fb60023' || p === '2c98ef5f95eef6700195ef4568560022') {
                        if (!phone) {
                            showWindwsText("请输入办理的手机号码");
                            return;
                        }
                        if (!isPhone(phone)) {
                            showWindwsText("请输入正确的手机号码");
                            return;
                        }
                        pageDataFunction(phone, 2, sanfang_uuid)
                        mg_createOrderSanfang()
                        return
                    }
                    // 辽宁移动插码
                    if (p === '2c98ef5f94170fed01941a58e4bf002f' || p === '2c98ef5f94170fed01941a57bac2002e' || p === '2c98ef5f947d3d98019482758a700028' || p === '2c98ef5f94b1cf770194d504518702fa') {
                        lnlt_jscode(2, phone, p)
                    }
                    // 广东移动插码
                    if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                        || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                        || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                        || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                        || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                        || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                        || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                        || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                        || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                        || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                    ) {
                        const orderId = generateOrderId(12); // 生成订单号
                        sessionStorage.setItem('currentOrderId', orderId); // 保存订单号
                        event_upload("提交订单", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(phone), orderId, null, null, pid = p);
                    }
                    // 广西移动插码
                    if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                        const orderId = generateOrderId(6); // 生成订单号
                        sessionStorage.setItem('currentOrderId', orderId); // 保存订单号
                        guangXi_event_upload(type = "确认办理", pId = p, apiName = '流量，套餐业务办理接口', apiUrl = 'https://cardstatic.zjhrnet.com/cardfr/clcommonapi/createOrder', aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = orderId);
                    }
                    if (!phone) {
                        showWindwsText("请输入办理的手机号码");
                        return;
                    }
                    if (!isPhone(phone)) {
                        showWindwsText("请输入正确的手机号码");
                        return;
                    }
                    if ((url.includes('ad17df9ec5b04db5a558b9d23f0ef5ae') || url.includes('d456d49b735946e387d849a391e07307')) && flag) {
                        // 广点通链接显示弹窗
                        autoLinkResult()
                        return
                    }
                    if (smsPhone == "") {
                        showWindwsText("请获取验证码");
                        return;
                    }
                    if (!code) {
                        showWindwsText("请完整填写验证码");
                        return;
                    }
                    if ($("#phone").val() != smsPhone) {
                        message.info("调皮！请输入获取验证码的手机号");
                        return;
                    }
                    if (!/^[0-9]+$/.test(code)) {
                        showWindwsText("请输入正确的验证码");
                        return;
                    }
                    //判定是否显示二次弹窗
                    // if (autoInfo.isConfirm === true) {
                    //     $('.confirm-new').css({
                    //         // "background-image": `url("${infoObj.confirmImg}") no-repeat`,
                    //         "background-image": `url("${infoObj.confirmImg}")`,
                    //     })
                    //     $('.confirm-mask').show()
                    // }
                    if (autoInfo.type === 1 && autoInfo.secondPopUrl) {
                        $('.confirm-new').css({
                            "background-image": `url("${autoInfo.secondPopUrl}")`,
                        })
                        $('.confirm-mask').show()
                        if (url.includes('page=a9ffa39e0c274a879666d82cda29e66d')) {
                            sth_ag.checkSmsFront({
                                phoneNumber: phone,
                                smsCode: code
                            }).then(res => {
                                console.log(res)
                                if (res.resultCode == '0000') {
                                    console.log('短信验证码校验前置处理成功，uuid2=' + res.data.uuid2);
                                    gzlt_uuid2 = res.data.uuid2
                                    // mg_createOrder()
                                } else {
                                    console.log('短信验证码校验前置处理失败，失败原因：' + res.resultMsg)
                                }
                            })
                        }
                    } else if (autoInfo.type === 2 && infoObj.doubleCheckImgUrl) {
                        $('.confirm-new').css({
                            "background-image": `url("${infoObj.doubleCheckImgUrl}")`,
                        })
                        $('.confirm-mask').show()
                    } else {
                        if (url.includes('page=4d64b1beb2a3463497d90f699108cfa5')) {
                            sth_ag.checkSmsFront({
                                phoneNumber: phone,
                                smsCode: code
                            }).then(res => {
                                console.log(res)
                                if (res.resultCode == '0000') {
                                    console.log('短信验证码校验前置处理成功，uuid2=' + res.data.uuid2);
                                    gzlt_uuid2 = res.data.uuid2
                                    setTimeout(() => {
                                        mg_createOrder()
                                    }, 500)
                                    // mg_createOrder()
                                } else {
                                    console.log('短信验证码校验前置处理失败，失败原因：' + res.resultMsg)
                                }
                            })
                        } else {
                            mg_createOrder()
                        }
                    }
                });
                //下单
                const mg_createOrder = function () {
                    pageUrlName = document.title
                    // console.log(pageUrlName)
                    fullUrl = getFullUrl(p)
                    console.log(fullUrl)
                    if (bd_vid) {
                        clickId = url
                    }
                    if (track_id) {
                        clickId = track_id
                    }
                    $(".load_bg").show();
                    let data = JSON.stringify({
                        pid: p,
                        acid: c,
                        mobile: phone,
                        a_oId: a_oId,
                        clickid: clickId,
                        unionSite: unionSite,
                        smsCode: $("#code").val(),
                        reqSn: sendSms_payload.reqSn,
                        telcoOrderNo: sendSms_payload.telcoOrderNo,
                        remark: sendSms_payload.remark,
                        other: sendSms_payload.other,
                        ahLoginToken: ahLoginToken,
                        // 秋末电信js插码字段
                        voucherNumber: voucherNumber,
                        entranName: packageName,
                        // 页面名称
                        pageUrlName: pageUrlName,
                        pageUrl: fullUrl,
                        // 贵州联通js插码字段
                        gzltUuid2: gzlt_uuid2
                    });
                    pageData_auto(c, phone, p, 6,);
                    // 辽宁移动插码
                    if (p === '2c98ef5f94170fed01941a58e4bf002f' || p === '2c98ef5f94170fed01941a57bac2002e' || p === '2c98ef5f947d3d98019482758a700028' || p === '2c98ef5f94b1cf770194d504518702fa') {
                        lnlt_jscode(4, phone, p)
                    }
                    //return
                    GmAjax(
                        "POST",
                        "clcommonapi/createOrder",
                        "application/json",
                        data,
                        "json",
                        function (res) {
                            if (res.status == 200 || res.status == 201) {
                                console.log(res)
                                pageData_auto(c, phone, p, 7,);
                                // 广东移动插码
                                if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                                    || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                                    || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                                    || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                                    || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                                    || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                                    || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                                    || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                                    || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                                    || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                                ) {
                                    const orderId = sessionStorage.getItem('currentOrderId'); // 获取之前保存的订单号
                                    event_upload("办理成功", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(phone), orderId, res.payload.telCoOrderNO, null, pid = p);
                                }
                                // 广西移动插码
                                if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                                    const orderId = sessionStorage.getItem('currentOrderId'); // 获取之前保存的订单号
                                    guangXi_event_upload(type = "办理成功", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = res.payload.telCoOrderNo, businessId = orderId);
                                }
                                $("#phone").val("");
                                $("#code").val("");
                                setTimeout(function () {
                                    $(".load_bg,.loading").hide()
                                    $(".bg_2").hide()
                                    if (autoInfo.type === 1) {
                                        if (url.includes('87cf015ae2284a5097b89bbc639835b3')) {
                                            location.href = res.payload.reqUrl
                                        }
                                            // 测试使用
                                            // else if (url.includes('page=5b767e7604f84a8f8a2241455ea6e52f&p=2c98ef5f9565a2a001956f73f7f408de')) {
                                            //     console.log(1111111111)
                                        // }
                                        else {
                                            location.href = `../page-public/mode_auto_page_success/successh.html${location.search}&pid=${p}&phone=${phone}&succurl=${autoInfo.successPopUrl}`
                                        }
                                    }
                                    if (autoInfo.type === 2) {
                                        if (p === '2c98ef5f9352722501936cab4c9d01b9') {
                                            location.href = res.payload.reqUrl
                                        } else {
                                            location.href = `../page-public/mode_auto_page_success/success.html${location.search}&pid=${p}&acid=${c}&phone=${phone}&pageUrlId=${pageUrlId}&succurl=${infoObj.successPopUrl}`
                                        }
                                    }
                                }, 500);
                            } else {
                                pageData_auto(c, phone, p, 8,)
                                // 广东移动插码
                                if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                                    || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                                    || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                                    || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                                    || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                                    || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                                    || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                                    || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                                    || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                                    || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                                ) {
                                    const errorMessage = res.error
                                    const orderId = sessionStorage.getItem('currentOrderId'); // 获取之前保存的订单号
                                    event_upload("办理失败", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(phone), orderId, res.payload.telCoOrderNO, errorMessage, pid = p);
                                }
                                // 广西移动插码
                                if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                                    const errorMessage = res.error
                                    const orderId = sessionStorage.getItem('currentOrderId'); // 获取之前保存的订单号
                                    guangXi_event_upload(type = "办理失败", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = errorMessage, orderNumber = res.payload.telCoOrderNo, businessId = orderId);
                                }
                                $(".load_bg").hide()
                                if (res.error.includes('验证码') || res.error.includes('随机码')) {
                                    showWindwsText(`${res.error}`)
                                } else {
                                    if (autoInfo.type === 1) {
                                        message.info(`${res.error}`)
                                    }
                                    if (autoInfo.type === 2) {
                                        message.info(`${res.error}`)
                                        getAd('order_error', 2, 1)
                                        // if(productList.length > 1){
                                        //     for (let index = 0; index < productList.length; index++) {
                                        //         const item = productList[index];
                                        //         if (p === item.fusionProId) {
                                        //             const nextItem = productList[index + 1];
                                        //             if (nextItem) {
                                        //                 updateUI(nextItem);
                                        //                 return
                                        //             } else {
                                        //                 getAd('order_error', 2)
                                        //             }
                                        //         }
                                        //     }
                                        // }else{
                                        //     getAd('order_error', 2)
                                        // }
                                    }
                                }
                            }
                        }
                    );
                };

                //咪咕数媒下单逻辑代码
                const mg_createOrderMiGu = function () {
                    $(".load_bg").show();
                    let data_migu = JSON.stringify({
                        pid: p,
                        acid: c,
                        mobile: phone,
                        a_oId: a_oId,
                        clickid: clickId,
                        unionSite: unionSite,
                        smsCode: $("#code").val(),
                        reqSn: reqSn,
                        telcoOrderNo: telcoOrderNo,
                        remark: remark,
                        entranName: packageName,
                    });
                    pageData_auto(c, phone, p, 6,);
                    GmAjax('POST', 'clcommonapi/createOrder', 'application/json', data_migu, 'json', function (res) {
                        $('.load_bg,.loading').hide()
                        $('.bg_2').hide()
                        if (res.status == 200 || res.status == 201) {
                            pageData_auto(c, phone, p, 7,);
                            $(".load_bg,.loading").hide()
                            $(".bg_2").hide()

                            function base64EncodePhone(phoneNumber) {
                                // 创建一个 TextEncoder 对象
                                const encoder = new TextEncoder();
                                // 将手机号字符串转换为 Uint8Array
                                const encoded = encoder.encode(phoneNumber);
                                // 使用 btoa() 函数进行 Base64 编码
                                const base64Encoded = btoa(String.fromCharCode.apply(null, encoded));
                                return base64Encoded;
                            }

                            const misidn = base64EncodePhone(phone);
                            console.log(res.payload)
                            var payjson = {
                                "orderId": res.payload.telCoOrderNO,
                                // "orderId": '0222410112009414399205',
                                "channelCode": "M3GP0001",
                                "productId": "92697508",
                                "misidn": misidn,
                                "actionType": "1",
                            };
                            if (p === '2c98ef5f9482ee97019482f3a8330002') {
                                payjson.channelCode = 'M3GP0002'
                                payjson.productId = '15335368'
                            }
                            console.log(payjson)
                            window.MiguSdk.migupay(payjson, function (resultCode, msg, data) {
                                console.log(resultCode)
                                console.log(msg)
                                console.log(data)
                                // message.info(resultCode)
                                console.log(typeof resultCode)
                                if (resultCode === '0000' || resultCode === '4901') {
                                    // location.href = `./success.html${location.search}&phone=${phone}`;
                                    message.info('订购成功')
                                    location.href = `../page-public/mode_auto_page_success/success.html${location.search}&pid=${p}&acid=${c}&phone=${phone}&pageUrlId=${pageUrlId}&succurl=${infoObj.successPopUrl}`
                                } else {
                                    setTimeout(() => {
                                        // message.info(msg)
                                        getAd('order_error', 2, 1)
                                    }, 2000);
                                }
                            })
                        } else {
                            pageData_auto(c, phone, p, 8,)
                            $(".load_bg").hide()
                            message.info(res.error)
                            getAd('order_error', 2, 1)
                        }
                    })
                }
                //网易云游戏定向包下单逻辑代码
                const mg_createOrderWangYiYun = function () {
                    $(".load_bg").show()
                    pageData_auto(c, phone, p, 6,)
                    let data = JSON.stringify({
                        pid: p,
                        acid: c,
                        mobile: phone,
                        a_oId: a_oId,
                        clickid: clickId,
                        unionSite: unionSite,
                        smsCode: $("#code").val(),
                        reqSn: reqSn,
                        telcoOrderNo: telcoOrderNo,
                        remark: remark,
                        entranName: packageName,
                        payType: 2
                    })
                    GmAjax('POST', 'clcommonapi/createOrder', 'application/json', data, 'json', function (res) {
                        if (res.status == 200 || res.status == 201) {
                            pageData_auto(c, phone, p, 7,)
                            $(".load_bg,.loading").hide()
                            $(".bg_2").hide()
                            setTimeout(function () {
                                // location.href = res.payload.reqUrl
                                $('.iframe_main').attr('src', `${res.payload.reqUrl}`)
                                $('.form').hide()
                                $('.iframe').show(100)
                            }, 300)
                        } else {
                            pageData_auto(c, phone, p, 8,)
                            $(".load_bg").hide()
                            message.info(res.error)
                            getAd('order_error', 2, 1)
                        }
                    })
                }
                //三方支付下单逻辑代码
                const mg_createOrderSanfang = function () {
                    //中心打点_click
                    WoAnalytics.addPoint({
                        ad: '070f2d9b0319468ab73073842296b9ed',
                        ett: 'click',
                        ea: 'activity_detail_order',
                        cpn: 'activity_detail'
                    })
                    $(".load_bg").show()
                    pageData_auto(c, phone, p, 6,)
                    let data = JSON.stringify({
                        pid: p,
                        acid: c,
                        mobile: phone,
                        clickid: clickId,
                        unionSite: unionSite,
                        adid: adid,
                        requestid: requestid,
                        wokey: wokey,
                        userip: userIp,
                        ua: `${navigator.userAgent}`,
                        payType: 'wx',
                        isOpenContinuePackage: 1,
                        entranName: packageName,
                        backUrl: `https://cardstatic.zjhrnet.com/front/h5-s-n177bhbk68vxfq/success.html`,
                    })
                    GmAjax('POST', 'clcommonapi/createOrder', 'application/json', data, 'json', function (res) {
                        if (res.status == 200 || res.status == 201) {
                            pageData_auto(c, phone, p, 7,)
                            $(".load_bg,.loading").hide()
                            $(".bg_2").hide()
                            setTimeout(function () {
                                location.href = res.payload.reqUrl
                            }, 300)
                        } else {
                            pageData_auto(c, phone, p, 8,)
                            $(".load_bg").hide()
                            message.info(res.error)
                            getAd('order_error', 2, 1)
                        }
                    })
                }
                //弹窗
                $(".confirm-btn2").click(function () {
                    $(".confirm-mask").hide();
                    mg_createOrder();
                });
                $(".confirm-btn1,.confirm-new .close").click(function () {
                    $(".confirm-mask").hide();
                });
                // 广点通弹窗点击事件
                $(".show_windws_text_2 .butt .b").click(function () {
                    $(".show_windws_text_2").hide()
                    $(".form-ys-radio").addClass("form-ys-radio-active");
                    // getinfo(phone, '')
                    if (packageName) {
                        checkPackage(c, p, packageName)
                            .then(res => {
                                // console.log(res.status)
                                if (res.status === 200 && res.payload) {
                                    autoLinkResult()
                                } else {
                                    getAd('order_error', 2, 2)
                                }
                            })
                            .catch(err => {
                                getAd('order_error', 2, 2)
                            })
                    } else {
                        autoLinkResult()
                    }
                })
                $(".show_windws_text_2 .butt .c").click(function () {
                    flag = true;
                    $(".show_windws_text_2").hide()
                    $(".form-ys-radio").removeClass("form-ys-radio-active");
                })
                // 悦蓝审核页客服弹窗点击事件
                $(".service-pop").click(function () {
                    $(".popup-bg").show()
                    $(".popup-close").show()
                })
                $(".popup-close").click(function () {
                    $(".popup-bg").hide()
                    $(".popup-close").hide()
                })
                $(".popup-bg .service-popup .service-button").click(function () {
                    window.location.href = "tel:************"
                })
                $('#code').bind("input propertychange", function (e) {
                    let length = $('#code').val().length
                    if (length && smsPhone) {
                        $('.form_img_3').hide()
                        if (autoInfo.type === 2) {
                            if (infoObj.autoOrder === 1 && length == 4) {
                                // 咪咕数媒逻辑
                                if (p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002') {
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrderMiGu()
                                } else if (p === '2c98ef5f96ed10b00196fc037d9601c5') {
                                    // 网易云逻辑
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrderWangYiYun()
                                } else if (p === '2c98ef5f95eef6700195ef461fb60023' || p === '2c98ef5f95eef6700195ef4568560022') {
                                    // 三方支付逻辑
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrderSanfang()
                                } else {
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrder()
                                }
                            }
                            if (infoObj.autoOrder === 2 && length == 6) {
                                // 咪咕数媒逻辑
                                if (p === '2c98ef5f926a105401926f0e0c02006c' || p === '2c98ef5f9482ee97019482f3a8330002') {
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrderMiGu()
                                } else if (p === '2c98ef5f96ed10b00196fc037d9601c5') {
                                    // 网易云逻辑
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrderWangYiYun()
                                } else if (p === '2c98ef5f95eef6700195ef461fb60023' || p === '2c98ef5f95eef6700195ef4568560022') {
                                    // 三方支付逻辑
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrderSanfang()
                                } else {
                                    pageData_auto(c, phone, p, 9)
                                    mg_createOrder()
                                }
                            }
                        }
                    } else {
                        if (autoInfo.guidelines) {
                            $('.form_img_3').show()
                        } else {
                            $('.form_img_3').hide()
                        }
                    }

                    // if ((infoObj.res.p === '广东' && infoObj.res.cmcc === '联通') || (infoObj.res.p === '河北' && infoObj.res.cmcc === '联通')) {
                    //     if (length === 6) {
                    //         const $handDiv = $('<div>').addClass('hand-div');
                    //         $('.form').append($handDiv);
                    //         $('.hand-div')
                    //             .css({
                    //                 width: '3.68rem',
                    //                 margin: 'auto',
                    //                 height: ' .83rem',
                    //                 position: 'absolute',
                    //                 top: '4.9rem',
                    //                 left: '1.1rem',
                    //                 // transform: 'translateX(-50%)',
                    //                 // right: '0',
                    //                 zIndex: '99',
                    //                 background: `url(https://oss.zjhrnet.com/img/cunliang/dianxin/hndx/refresh/hand.gif) no-repeat`,
                    //                 backgroundSize: 'contain',
                    //                 backgroundPosition: 'center'
                    //             });
                    //     }
                    // }
                })
                //失去焦点
                $("#code").blur(function () {
                    let length = $('#code').val().length;
                    if (isGetCode && length >= 4) {
                        pageData_auto(c, phone, p, 9)
                        // 广东移动插码
                        if (p === '2c98ef5f9565a2a001956f73f7f408de' || p === '2c98ef5f9565a2a001956f81807608f5'
                            || p === '2c98ef5f9565a2a001956f83ee7b0903' || p === '2c98ef5f97682c9701976863a5400015'
                            || p === '2c98ef5f97d4da110197e2a3b6f5002b' || p === '2c98ef5f97d4da110197e8d6c32d0100'
                            || p === '2c98ef5f985f67810198635e4d70004d' || p === '2c98ef5f98acb11b0198acffeac9000b'
                            || p === '2c98ef5f98cb75d40198cf64ba0a005c' || p === '2c98ef5f98cb75d40198cf67bb1a005e'
                            || p === '2c98ef5f98cb75d40198e4e75e63091a' || p === '2c98ef5f98ee800d0198ef308a330011'
                            || p === '2c98ef5f98ee800d0198ef2c4cdf000f' || p === '2c98ef5f98ee800d0198ef28f469000d'
                            || p === '2c98ef5f98f3bf310198f3c6b50b0002' || p === '2c98ef5f990e930c0199128cf7610506'
                            || p === '2c98ef5f98cb75d40198cf6353bf005b' || p === '2c98ef5f97d4da110197e2a32f7d002a'
                            || p === '2c98ef5f98cb75d40198cf666db3005d' || p === '2c98ef5f97682c9701976862f4d70014'
                        ) {
                            event_upload("输入短信验证码", apiId = null, apiName = null, aesPhone = encryptAES(phone), null, null, null, pid = p);
                        }
                        // 广西移动插码
                        if (p === '2c98ef5f9815fdb40198160e27c40002' || p === '2c98ef5f981658290198178f0c44004c' || p === '2c98ef5f98f3ed370199042a198304bd') {
                            guangXi_event_upload(type = "输入短信验证码", pId = p, apiName = null, apiUrl = null, aesPhone = encryptAES(phone), errorCode = null, errorMsg = null, orderNumber = null, businessId = null);
                        }
                    }
                });
                //活动说明
                $(".form-ys-p3").click(function () {
                    // 链接特殊处理滑动至图片活动说明https://cardstatic.zjhrnet.com/front/autopage/index.html?page=14fafea41ad148fcbfcaf212b93f9cfc&p=2c98ef5f9858ec19019859ffa6c0019d&c=2463068a490b4aa6896247285a23c65e
                    if (url.includes('page=14fafea41ad148fcbfcaf212b93f9cfc')) {
                        $("html,body").animate(
                            {
                                scrollTop: $(".pro-img").offset().top + 350
                            },
                            500
                        );
                    }
                    if ($(".rule-title").css("display") !== "none") {
                        $("html,body").animate(
                            {
                                scrollTop: $(".rule-title").offset().top,
                            },
                            500
                        );
                    }
                });
                //隐私政策
                $(".form-ys-p2").click(function () {
                    if (url.includes('page=e06f54f21f4e48968955ad2588f18b24') || url.includes('page=5f1167f5aeb84ecca8573c1fb7286b2f')) {
                        $(".protocol_box .title").html("业务受理协议");
                    } else {
                        $(".protocol_box .title").html("隐私条款");
                    }
                    const defaultAgreementContent = `
                        <h1>个人信息授权与保护声明</h1>
                        <p><span class="blod">本页面由浙江弘瑞网络技术有限公司（以下简称为"我们"或"弘瑞网络"）开发。</span> 请您务必仔细阅读并了解本《个人信息授权与保护声明》（以下简称为<span class="blod">"本声明"</span>）。<span class="blod">对于重要条款我们采用粗体及下划线方式进行显著标注以提示您注意。</span></p>
                        <p class="blod">您一旦主动在页面填写您的个人信息并进行提交操作，即意味着您同意我们按照本声明收集、使用、共享您的相关信息。若您不接受本声明，请勿登记您的个人信息。</p>
                        <p>我们的页面和服务主要面向成人。我们不会主动收集未成年人的个人信息，如果您未满18周岁，请您在监护人陪同下仔细阅读并充分理解本声明，并征得监护人的同意后使用我们的服务或向我们提供信息。</p>
                        <h2>一、收集信息的原则及目的</h2>
                        <p>1. 保障为您所提供的产品或服务功能的正常实现。</p>
                        <p>2. 实现对您的推送功能。</p>
                        <p>3. 帮助我们维护和改进我们的产品或服务，提升用户体验。</p>
                        <h2>二、如何收集和使用您的个人信息</h2>
                        <p>我们将合法合规对您的个人信息进行采集、使用。</p>
                        <p class="blod">您已知晓并同意在您下次浏览本落地页面时，我们会帮您预先填写您上次输入的历史信息以提升您的使用体验。您亦可拒绝使用预填充功能。</p>
                        <h3>（一）收集和使用您的个人信息的范围</h3>
                        <p class="blod">1. 下单及订单管理</p>
                        <p><span class="blod">为了向您配送货物或提供相关服务。当您准备登记您的信息以促成交易的达成时，我们将相应收集您的姓名、手机号、地址、性别和身份证号码（仅特殊行业需要，如运营商、保险）。 </span>同时，所涉订单中会载明订单编号、您所购买的商品或服务信息、下单时间等。具体需要填写的信息可能会根据我们提供的产品/服务的不同而有所差异，请以届时向您展示的产品/服务以及所对应的要求填写相关个人信息。 </p>
                        <p>2.广告的定向推送</p>
                        <p class="blod">为实现广告推送功能，在您进入本广告页面并同意本声明时，我们还可能会收集您的产品与/或服务的设备信息，包括网络身份标识信息（手机IPV4地址、IP）、网络信息、手机硬件信息（电量、品牌、型号、内存、屏幕分辨率、CPU信息）和运营商信息、操作日志。</p>
                        <h3>（二）例外</h3>
                        <p>对于《信息安全技术 个人信息安全规范》5.6中规定的情形，我们收集、使用您的个人信息无需事先征得您的授权同意。</p>
                        <h2>三、如何共享、转让您的个人信息</h2>
                        <h3>（一）共享</h3>
                        <p> 仅为实现本声明之目的，我们可能会与<span class="blod">关联公司、合作伙伴</span> 共享您的某些个人信息。 <span class="blod">对我们与之共享个人信息的公司、组织和个人，我们会与其签署严格的合规协定或做类似要求，以规范其处理个人信息的行为，并促使其具备个人信息的安全保障能力。</span> 我们将与以下授权合作伙伴共享您的信息∶ </p>
                        <p class="blod">1.供应商、服务提供商和其他合作伙伴（包括相关代理商）。为向您进行货物配送及提供服务，我们可能会将您主动登记提供的信息向该商品及服务的提供商。以及为此目的所涉及的必要第三方进行共享，并由其在您已经向其授权的范围内使用，包括向您发送货物或与您进行必要的联系。</p>
                        <p class="blod">2.广告、分析服务类的授权合作伙伴。我们可能会将您的匿名化或去标识化的个人信息及您的设备信息与广告、分析服务类的授权合作伙伴共享，以帮助其在不识别您的个人身份的前提下提升广告有效触达率。 </p>
                        <h3>（二）转让</h3>
                        <p>我们不会将您的个人信息转让给任何第三方，但基于您的明确同意或法律法规要求或涉及合并、收购、分立、重组或破产清算的情况除外。</p>
                        <div>
                            <h2>四、如何保存与保护您的个人信息我们的信息保护规则∶</h2>
                            <p class="blod">1.收集到您的个人信息后，在保障供应商、服务提供商和其他合作伙伴或必要第三方能够凭借相关信息进行货物发送或服务提供的基础上，我们将通过技术手段及时对数据进行匿名化或去标识化处理。 </p>
                            <p>2.我们已使用符合业界标准的安全防护措施保护您提供的个人信息，防止数据遭到未经授权访问、公开披露等。</p>
                            <p><span class="blod">3.如果我们的管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改或毁坏，</span>我们将及时将事件相关情况及时向您告知，难以逐一告知时，我们会采取公告的方式。同时，我们还将主动上报个人信息安全事件的处置情况。</p>
                            <p>我们的信息保存方式∶</p>
                            <p>1.保存期限∶您提供的个人信息，将在上文所声明的服务提供过程中持续授权我们使用。在您删除个人信息时，我们将视为您撤回了对于该等个人信息收集、使用或共享的授权和同意。</p>
                            <p>2.保存地域∶<span class="blod">原则上，我们所采集到的您的个人信息将存储在中国境内。</span></p>
                        </div>
                        <h2>五、您对个人信息享有的权利</h2>
                        <p>按照中国相关的法律、法规、标准，我们保障您对自己的个人信息的更正权、删除权或授权撤回等相关权益。</p>
                        <p class="blod">为实现本第五条所述权利，您可通过本声明第七条"如何联系我们"中所列明的联系方式与我们进行联系，我们在验证主体身份，确认相关要求和理由后，为您进行相应的更正、删除及授权撤回。</p>
                        <h2>六、政策更新</h2>
                        <p> 基于服务优化或情况变更等事由，本声明会不定期进行更新。更新版本将<span class="blod">通过在官方网站、广告落地页面等我们正在运营或进行合作的各类相关产品中发出，也请您访问我们网站及软件以便及时了解最新的《个人信息授权与保护声明》。</span></p>
                        <h2>七、联系我们</h2>
                        <p>您可通过广告落地页中的客服电话联系我们，我们将会在15个工作日内给您反馈。</p>
                        <h2>八、争议解决</h2>
                        <p class="blod">本声明的涉诉争议由杭州市滨江区人民法院管辖。</p>
                    `;
                    if (autoInfo.type === 1 || autoInfo.type === 2) {
                        if (autoInfo.agreementContent) {
                            if (autoInfo.agreementContent === '协议内容' || autoInfo.agreementContent.length < 20 || autoInfo.agreementContent === '<p>协议内容</p>') {
                                $(".protocol_box .policy-content").html(`${defaultAgreementContent}`);
                            } else {
                                $(".protocol_box .policy-content").html(`${autoInfo.agreementContent}`)
                            }
                        } else {
                            $(".protocol_box .policy-content").html(`${defaultAgreementContent}`)
                        }
                    }
                    if (autoInfo.type === 2 && infoObj) {
                        if (infoObj.agreementContent) {
                            if (infoObj.agreementContent === '协议内容' || infoObj.agreementContent.length < 20 || infoObj.agreementContent === '<p>协议内容</p>') {
                                $(".protocol_box .policy-content").html(`${defaultAgreementContent}`);
                            } else {
                                $(".protocol_box .policy-content").html(`${infoObj.agreementContent}`)
                            }
                        } else {
                            $(".protocol_box .policy-content").html(`${defaultAgreementContent}`);
                        }
                    }
                    $(".bg").show();
                    $(".protocol_box").show();
                });
                //特殊页面解约条款
                $(".form-ys-p4").click(function () {
                    $(".protocol_box .title").html("解约条款");
                    $(".bg").show();
                    // if(url.includes('page=5b767e7604f84a8f8a2241455ea6e52f')){
                    $(".protocol_box .policy-content").html(`
                            <p>甲方：中国移动通信客户
                            </p><p>乙方：中国移动通信集团广东有限公司
                            </p><p>      甲方业务协议解约按类型分为“直接解约”和“有条件解约”两大场景。
                            </p><p>一、直接解约
                            </p><p>主要是涉及甲方直接订购的业务，乙方未进行任何优惠或补贴，甲方也未得从乙方得到实物或权益等相关的业务，采用无条件直接解约。
                            </p><p>二、有条件解约
                            </p><p>1.合约业务
                            </p><p>如涉及赠送终端、话费存送、设备租赁或承诺在网或承诺消费获得相应优惠等业务，一般不能直接解约，甲方在“完成优惠赔付、不影响二次销售的退货”等情况下，有条件解约，按解约标准和流程操作（如有事先签订赔付标准，按约定的赔付标准执行）。具体如下：
                            </p><p>（1）资金优惠：如话费存送、套餐折扣等。解约条件：根据优惠、减免情况支付违约金。赔付标准：甲方如提前终止协议，则甲方需将已履行月份获赠金额或优惠减免金额总额、以及未履行月份所有低消或套餐金额总额的一定比例（法律规定范围内）进行赔付。
                            </p><p>（2）业务赠送：如承诺消费送流量包、语音包、短信包等。解约条件：根据已赠送业务价值，支付合理的违约金。赔付标准：甲方如要求提前终止协议，则甲方需将已获赠业务进行价值折算进行赔付。非流量类（语音、短信等）按单价标准资费折算，流量类按实际单价优惠资费折算。
                            </p><p>（3）宽带业务：如网龄送宽带，承诺低消送宽带。解约条件：提前终止宽带业务需对初装费或拆机费进行赔付。赔付标准：甲方如要求提前终止协议，需根据前期活动约定的初装费用或拆机费用进行赔付。
                            </p><p>（4）实物赠送：如承诺消费送终端、充值卡等。解约条件：在不影响二次销售的前提下，退还赠送的实物；在无法退还实物的情况下，通过对实物进行折现，支付违约金。赔付标准：根据退货要求且不影响二次销售的前提下，可在完成赠送实物退货后解约；如赠送实物已不满足退货条件，则甲方需根据实物赠送价值及未履行承诺期限进行补偿。
                            </p><p>（5）权益赠送：如咪咕视频、爱奇艺、优酷会员权益等。解约条件：需根据活动约定退回方可解约，不能退回需按价值折算进行补偿（权益赠送协议签订中应明确约定赠送权益的价值或折算方法）。赔付标准：权益未使用可在完成赠送权益回退后解约；权益已领取、使用，则甲方需将已使用权益进行价值折算，赔偿后解约。
                            </p><p>（6）实物租赁：如家庭宽带机顶盒、光猫等。解约条件：确保设备完好的情况下，返还租赁物；如无法归还或涉及其他利益损失的，可通过折现，支付合理违约金。赔付标准：甲方如要求提前终止协议，需根据活动约定条件退回或赔偿租赁物，根据退货约定且不影响二次销售的前提下，可在完成租赁实物退回后解约（注，如租赁物非新：在确保设备完好的情况下，可在完成租赁物退货后解约）；如租赁物已不满足再次租赁条件（设备已无法正常使用），则甲方需根据租赁设备价值进行等价偿还。
                            </p><p>2.融合业务
                            </p><p>融合套餐、主副卡、统付、共享、家庭网、集团网、惠农网、双向亲情互打等业务，需在不影响其他甲方业务使用的情况下，根据具体情况进行退订或解除。
                            </p>
                            <img src="https://oss.zjhrnet.com/img/cunliang/liangtong/woyuedu/sanfangzhif/25/T12.png" alt="" style="width: 100%;">
                            <p>注：①主卡、共享者解除，应对其他成员尽到通知义务。
                            </p><p>②副卡、被共享者解除，不应对其他成员权益造成损失。
                            </p><p>3.吉祥号码业务
                            </p><p>如四连号、三连号等吉祥号码。解约条件：按吉祥号码低消和未履行协议期进行赔付。赔付标准：如甲方要求提前终止协议，则甲方应当按照未履行月份所有低消或套餐金额总额向乙方支付损失赔偿金，如甲方有签约减免或降档低消金额特殊情况的，采用前述同样标准执行。（参考最新吉祥号码等级对应的低消或套餐标准）。
                            </p><p>4.集团信息化业务
                            </p><p>解约条件：根据乙方投入成本补贴和未履行时间等进行价值折算赔付（协议签订中应明确约定相关业务价值或折算方法）。
                            </p><p>赔付标准：具体赔付标准按签订的协议条款折算。</p>
                        `);
                    // }
                    // if(url.includes('page=6a1f0028ba784d52a2641a74d41fb77f')){
                    //     $(".protocol_box .policy-content").html(`
                    //         <p>1、7折享30元小福券优惠:合约期12个月，合约期内每月收取21元(通过扣费30元，返还9元话费实现)，到期后若不取消业务，按30元/月收取。部分任我换客户办理小福券，小福券从次月1日开始生效扣费。。
                    //         </P><P>2、本优惠限办一次，客户办理的优惠存在12个月合约期，合约期间只允许向上调整会员等级，不得取消、向下变更或办理其他会员产品。合约期内，客户如办理过户、停机、销户、离网、营销活动中断等业务，视为违约。如客户违约则按已履行完月份*9元金额向我司支付违约金，合约期内尚未享受的优惠将不再享受。
                    //         </P><P>3、所含N选1权益仅当个自然月有效，每月需主动兑换权益，到期未兑换自动失效。
                    //         </P><P>4、本优惠方案通过先扣费后赠费实现折扣优惠。活动赠送的话费不能用于抵扣本产品费用及其他增值业务等费用。
                    //         </P><P>5、该产品合约期内无法直接退订，如需退订可到当地沟通100营业厅或致电10086解约处理。合约到期后，如需取消，客户可短信发送0000到10086或致电10086热线退订处理。</p>
                    //     `);
                    // }
                    $(".protocol_box").show();
                });
                $(".btn-box").click(function () {
                    $(".bg").hide();
                    $(".protocol_box").hide();
                });
                // 特殊页面400拨号功能
                $(".tel-phone").click(function () {
                    window.location.href = "tel:************";
                });
                // 怡通主体地址信息授权图片
                if (url.includes('hrlskw.gsgs10086.com')) {
                    if ($(".address")) {
                        $('.address').html(`
						宝卡信息服务（杭州）有限公司<br />
						地址：浙江省杭州市淳安县千岛湖镇阳光水岸度假村23幢4层<br />
						联系电话：************
					`)
                    }
                    if ($(".qudao-img")) {
                        $('.qudao-img').hide()
                    }
                }
                // 弘瑞主体地址信息
                if (url.includes('cardstatic.zjhrnet.com')) {
                    if ($(".address")) {
                        $('.address').html(`
						浙江弘瑞网络技术有限公司<br />
						浙江省杭州市滨江区浦沿街道滨文路 426号岩大房文苑大厦7楼771室<br />
						************
					`)
                    }
                }
            } else {
                location.href = '../404/index.html'
            }
        })
    })
})

