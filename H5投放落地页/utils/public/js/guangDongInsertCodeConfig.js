//事件函数
// document.write('<script src="https://oss.zjhrnet.com/js/utils/kuaifu/crypto-js.js"></script>');

// 商品配置映射表
const PRODUCT_CONFIG = {
    "2c98ef5f9565a2a001956f73f7f408de": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "XFQ20YJMYH12Y",
        goodsName: "20元小福券7折优惠",
        pageName: "广东移动-20元小福券7折优惠",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f9565a2a001956f83ee7b0903": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "XFQ30YJMYH12Y",
        goodsName: "30元小福券7折优惠",
        pageName: "广东移动-30元小福券7折优惠",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f97682c9701976863a5400015": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "GS.PMPPRO.100000001024",
        goodsName: "咪咕短剧都市精品会员",
        pageName: "广东移动-咪咕短剧都市精品会员",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f97d4da110197e2a3b6f5002b": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "XFQ30YJMYH12Y",
        goodsName: "30元小福券7折优惠",
        pageName: "广东移动-30元小福券7折优惠",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    "2c98ef5f97d4da110197e8d6c32d0100": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "XFQ20YJMYH12Y",
        goodsName: "20元小福券7折优惠",
        pageName: "广东移动-20元小福券7折优惠",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    "2c98ef5f985f67810198635e4d70004d": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.PMPPRO.100000001655",
        goodsName: "15元粤享短剧会员",
        pageName: "广东移动-15元粤享短剧会员",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    "2c98ef5f98cb75d40198cf64ba0a005c": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.prod.10086000060435",
        goodsName: "20元咪咕足球通月包",
        pageName: "广东移动-20元咪咕足球通月包",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    "2c98ef5f98cb75d40198cf67bb1a005e": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.prod.10086000060438",
        goodsName: "30元NBA联盟通月包",
        pageName: "广东移动-30元NBA联盟通月包",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    "2c98ef5f98cb75d40198e4e75e63091a": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.PMPPRO.100000001642",
        goodsName: "20元咪咕订阅号-童话视铃",
        pageName: "广东移动-20元咪咕订阅号-童话视铃",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    "2c98ef5f98ee800d0198ef28f469000d": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "GS.prod.10086000060435",
        goodsName: "20元咪咕足球通月包",
        pageName: "广东移动-20元咪咕足球通月包",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f98ee800d0198ef2c4cdf000f": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "GS.prod.10086000060438",
        goodsName: "30元NBA联盟通月包",
        pageName: "广东移动-30元NBA联盟通月包",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f98ee800d0198ef308a330011": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "GS.PMPPRO.100000001655",
        goodsName: "15元粤享短剧会员",
        pageName: "广东移动-15元粤享短剧会员",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f98f3bf310198f3c6b50b0002": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "GS.prod.10086000074556",
        goodsName: "6元咪咕音乐薪光追剧社订阅号",
        pageName: "广东移动-6元咪咕音乐薪光追剧社订阅号",
        merchantsId: "GDOPEN_2124413",
        merchantsName: "浙江弘瑞网络技术有限公司",
    },
    "2c98ef5f98acb11b0198acffeac9000b": {
        host: "cardstatic.zjhrnet.com",
        goodsId: "GS.PMPPRO.100000001023  ",
        goodsName: "咪咕短剧时尚精品会员",
        pageName: "广东移动-咪咕短剧时尚精品会员",
        merchantsId: "GDOPEN_2129215",
        merchantsName: "北京易橙天下科技有限公司",
    },
    "2c98ef5f990e930c0199128cf7610506": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.prod.10086000078742",
        goodsName: "19元小福券优享会员",
        pageName: "广东移动-19元小福券优享会员",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    // 实际产品ID破解使用
    "2c98ef5f98cb75d40198cf6353bf005b": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.prod.10086000060435",
        goodsName: "20元咪咕足球通月包",
        pageName: "广东移动-20元咪咕足球通月包",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    // 实际产品ID破解使用
    "2c98ef5f97d4da110197e2a32f7d002a": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "XFQ30YJMYH12Y",
        goodsName: "30元小福券7折优惠",
        pageName: "广东移动-30元小福券7折优惠",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    // 实际产品ID破解使用
    "2c98ef5f98cb75d40198cf666db3005d": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.prod.10086000060438",
        goodsName: "30元NBA联盟通月包",
        pageName: "广东移动-30元NBA联盟通月包",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },
    // 实际产品ID破解使用
    "2c98ef5f97682c9701976862f4d70014": {
        host: "h1.hzjieyingnet.cn",
        goodsId: "GS.PMPPRO.100000001024",
        goodsName: "咪咕短剧都市精品会员",
        pageName: "广东移动-咪咕短剧都市精品会员",
        merchantsId: "GDOPEN_2130016",
        merchantsName: "杭州捷赢技术有限公司",
    },

};

// 获取商品和商户配置信息
function getProductAndMerchantConfig(pId) {
    return PRODUCT_CONFIG[pId] || {
        host: "",
        goodsId: "",
        goodsName: "",
        pageName: "",
        merchantsId: "",
        merchantsName: "",
    };
}

// // 获取商户配置信息
// function getMerchantConfig() {
//     let url = window.location.href
//     if (url.includes('h1.hzjieyingnet.cn')) {
//         return {
//             merchantsId: "GDOPEN_2130016",
//             merchantsName: "杭州捷赢技术有限公司",
//         }
//     }
//     return {
//         merchantsId: "GDOPEN_2124413",
//         merchantsName: "浙江弘瑞网络技术有限公司",
//     }
// }

// 生成订单号
function generateOrderId(length) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * Math.pow(10, length)).toString().padStart(length - 1, '0');
    return `${year}${month}${day}${hour}${minute}${second}${random}`;
}

// 事件函数
function event_upload(type, apiId = null, apiName = null, aesPhone = null, platOrderNo = null, supplierOrderId = null, message = null, pId = null) {
    // 获取商品配置
    const productConfig = getProductAndMerchantConfig(pId);

    // 业务办理插码调用
    let data = {
        "businessType_var": "存量", // 属于存量业务则写“存量”
        "goodsId_var": productConfig.goodsId, // 业务办理时所属的商品ID。
        "goodsName_var": productConfig.goodsName, //业务办理时商品名称。
        "goodsType_var": "流量", //业务办理时商品类型。
        "payType_var": "话费", // 业务办理支付时的支付方式名称。（支付宝、微信……）
        "ordertype_var": "流量订单", // 业务办理支付完成时所属订单类型名称。
        "pageName_var": productConfig.pageName, // 页面访问时当前页面名称，传具体的页面名称。
        "merchantsId_var": productConfig.merchantsId, // 商品所属商户id。--弘瑞ID
        "merchantsIdCity_var": "省统", //商户ID归属地市，枚举值：省统、广州、中山、云浮……
        "merchants_var": productConfig.merchantsName // 商品所属商户名称。
    }

    data.processType_var = type; // 业务办理时的每个办理步骤。（页面浏览、输入电话号码、获取短信验证码、输入短信验证码、确认办理、立即办理、二次确认-确认、身份认证、二次确认-取消、提交订单、提交订单成功、提交订单失败、办理成功、办理失败、支付成功、支付失败）
    if (apiId != null) {
        data.apiId_var = apiId; // 调用接口id
    }
    if (apiName != null) {
        data.apiName_var = apiName; // 调用接口名称。
    }
    if (aesPhone != null) {
        data.phoneNumber_var = aesPhone; //输入电话号码,传AES加密后的手机号
    } else {
        aesPhone = "无";
    }
    if (platOrderNo != null) {
        data.merchantsOrder_var = platOrderNo; // 自建的订单号（提交订单时赋值）。
    } else {
        data.merchantsOrder_var = "";
    }
    if (supplierOrderId != null) {
        data.orderNumber_var = supplierOrderId; // 接口返回的订单号（提交订单成功或办理成功时赋值）。
    } else {
        data.orderNumber_var = "";
    }
    if (message != null) {
        data.errorMessage_var = message; // 业务办理失败时的失败原因明细。（网络问题、业务互斥……）
    } else {
        message = "无";
    }
    console.log("插码数据", data);
    gdp('track', "merchants_bussinessProcessing", data);
}

// 加密函数（AES-128-ECB + PKCS7填充）
function encryptAES(data) {
    // 将密钥转换为CryptoJS的WordArray格式
    const keyBytes = CryptoJS.enc.Utf8.parse('9e5702ead4d643fd');
    // 加密配置：ECB模式、PKCS7填充
    const cfg = {
        padding: CryptoJS.pad.Pkcs7,
        mode: CryptoJS.mode.ECB,
    };
    // 加密数据
    const encrypted = CryptoJS.AES.encrypt(data, keyBytes, cfg);
    // 返回Base64编码结果
    return encrypted.toString();
}


// event_upload("获取短信验证码", "smscodeapply", apiName = "短信验证码申请", aesPhone = encryptAES(tel));
//
// event_upload("提交订单", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(tel));
//
// event_upload("办理失败", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(tel), data.data.chaMaData.order_num, data.data.chaMaData.isp_order_num, message = data.message);
//
// event_upload("办理成功", "smscodechkodcommitorder", apiName = "短信验证及订单提交", aesPhone = encryptAES(tel), data.data.chaMaData.order_num, data.data.chaMaData.isp_order_num, message = data.message);
//
// event_upload("输入电话号码", apiId = null, apiName = null, aesPhone = encryptAES(tel));
//
// event_upload("输入短信验证码", apiId = null, apiName = null, aesPhone = encryptAES(tel));
