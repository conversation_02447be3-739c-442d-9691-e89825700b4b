/*
* @param {string} province - 省份名称
* @param {string[]} city - 城市名称列表
* @param {string[]} shieldedCities - 屏蔽城市名称列表
* @param {string} operator - 运营商名称
* @param {string[]} specialProIds - 特殊产品融合ID列表
* @param {object} timeRange - 投放时间范围对象(24点需要写为23:59)
* @param {string[]} shieldedPackageName - 屏蔽包名列表
*/

// 定义特殊时间段和产品ID的配置
const specialConfigs = [
    {
        province: '福建',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f93d331b20193d3a7864d0016'],
        timeRange: {startHour: 6, endHour: 23, endMinute: 30}
    },
    {
        province: '辽宁',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f93d331b20193d3c146e50018'],
        timeRange: {startHour: 7, endHour: 22}
    },
    {
        province: '广西',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f93d331b20193d766658d0039'],
        timeRange: {startHour: 8, endHour: 23}
    },
    {
        province: '广东',
        city: ['东莞'],
        operator: '电信',
        specialProIds: ['2c98ef5f93bdc4e70193cd93afa70085'],
        timeRange: null
    },
    {
        province: '河南',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f93d331b20193d7696a1c003a'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '云南',
        city: [],
        operator: '电信',
        specialProIds: ['2c98ef5f93d331b20193d774612e0043'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '黑龙江',
        city: [],
        operator: '电信',
        specialProIds: ['2c98ef5f93d331b20193d77cdad70045'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '青海',
        city: [],
        operator: '电信',
        specialProIds: ['2c98ef5f93d331b20193d791aaf7004d'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '湖北',
        city: ['襄阳', '荆州', '恩施'],
        operator: '电信',
        specialProIds: ['2c98ef5f93d331b20193d77203d30041'],
        timeRange: null
    },
    {
        province: '江苏',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9400dba0019402128f67003f'],
        timeRange: {startHour: 6, endHour: 23, endMinute: 59}
    },
    {
        province: '四川',
        city: [],
        shieldedCities: ['成都'],
        operator: '移动',
        specialProIds: ['2c98ef5f9400dba00194152b843e0189'],
        timeRange: null
    },
    {
        province: '新疆',
        city: [],
        shieldedCities: ['吐鲁番', '和田', '阿克苏', '喀什', '巴音郭楞', '克孜勒苏', '石河子', '伊犁', '阿勒泰'],
        operator: '电信',
        specialProIds: ['2c98ef5f9400dba001941543ccdd0190'],
        timeRange: null
    },
    {
        province: '河北',
        city: [],
        shieldedCities: ['秦皇岛'],
        operator: '联通',
        specialProIds: [],
        timeRange: null
    },
    {
        province: '安徽',
        city: [],
        shieldedCities: ['蚌埠', '阜阳'],
        operator: '联通',
        specialProIds: [],
        timeRange: null
    },
    {
        province: '贵州',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9400dba00194167dc5d201ca'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '新疆',
        city: [],
        operator: '电信',
        specialProIds: ['2c98ef5f9400dba001941543ccdd0190'],
        timeRange: {startHour: 9, startMinute: 30, endHour: 1,}
    },
    // 咪咕数媒时间限制：晚23点-次日7点前暂停推广
    // 地域：海南、河南、青海、内蒙古
    {
        province: '',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f935272250193629d40ec009d'],
        timeRange: {startHour: 7, endHour: 23,}
    },
    // 咪咕数媒时间限制：晚23点-次日7点前暂停推广
    // 地域：海南、河南、青海、内蒙古
    {
        province: '',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f95125d0d0195185e5767009d'],
        timeRange: {startHour: 7, endHour: 23,}
    },
    {
        province: '四川',
        city: [],
        shieldedCities: ['雅安'],
        operator: '移动',
        specialProIds: ['2c98ef5f9443c25901945e269fc501a1'],
        timeRange: null
    },
    {
        province: '四川',
        city: [],
        shieldedCities: ['雅安', '成都'],
        operator: '移动',
        specialProIds: ['2c98ef5f9443c25901945e5851c801bb'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9443c25901945e5851c801bb'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '江苏',
        city: [],
        shieldedCities: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9443c25901945e5851c801bb'],
        timeRange: {startHour: 9, endHour: 21}
    },
    {
        province: '内蒙古',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9482ee970194874973f4005f'],
        timeRange: {startHour: 7, endHour: 23,}
    },
    {
        province: '内蒙古',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9482ee970194874b6ea20061'],
        timeRange: {startHour: 7, endHour: 23,}
    },
    {
        province: '广西',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f948dfd5d01949125d07c003d'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '吉林',
        city: [],
        operator: '移动',
        specialProIds: ['2c98ef5f948dfd5d019490f3a2a2001e'],
        timeRange: {startHour: 7, endHour: 23}
    },
    {
        province: '广东',
        city: ['广州'],
        operator: '电信',
        specialProIds: ['2c98ef5f948dfd5d01949127b7a3003f'],
        timeRange: null
    },
    {
        province: '广西',
        city: [''],
        operator: '移动',
        specialProIds: ['2c98ef5f948dfd5d0194925996220187'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '广西',
        city: [''],
        operator: '电信',
        specialProIds: ['2c98ef5f948dfd5d01949251e86c0172'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '浙江',
        city: [''],
        operator: '电信',
        specialProIds: ['2c98ef5f948dfd5d019491b364ec00b1'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        province: '甘肃',
        city: [''],
        operator: '移动',
        specialProIds: ['2c98ef5f948dfd5d019491c5149500bc'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 30}
    },
    {
        province: '内蒙古',
        city: [''],
        operator: '移动',
        specialProIds: ['2c98ef5f9496c378019496d76ad30023'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 三发-重庆移动-5G福袋流量冲浪包20元
        province: '重庆',
        city: [''],
        operator: '移动',
        specialProIds: ['2c98ef5f9400dba0019415380238018c'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 咪咕数媒时间限制：晚23点-次日7点前暂停推广
    // 地域：海南、河南、青海、内蒙古
    {
        // 咪咕数媒-咪咕短剧超级会员20元-优化模式融合版
        province: '',
        city: [''],
        operator: '移动',
        specialProIds: ['2c98ef5f9488f97f01948dd03a950086'],
        timeRange: {startHour: 7, endHour: 23}
    },
    {
        // 秋末-辽宁联通-视频彩铃应用型产品尊享微剧版25元
        province: '辽宁',
        city: [''],
        operator: '联通',
        specialProIds: ['2c98ef5f93424e6e019342e7785b001e'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 秋末-辽宁联通-云加速（专业版）通用流量包25元
        province: '辽宁',
        city: [''],
        operator: '联通',
        specialProIds: ['2c98ef5f921d728f0192221cfb380058'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 三发-四川移动-明星来电A包24元
        province: '四川',
        city: [],
        operator: '移动',
        shieldedCities: ['雅安'],
        specialProIds: ['2c98ef5f95125d0d019513096aa20035'],
        timeRange: null
    },
    {
        // 秋末-辽宁联通-30元商户立减金-融合版
        province: '辽宁',
        city: [],
        operator: '联通',
        shieldedCities: [],
        specialProIds: ['2c98ef5f95a886500195a8bf3dfd001e'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 秋末-电信权益商城尊享会员
        province: '',
        city: [],
        operator: '电信',
        shieldedCities: [],
        specialProIds: ['2c98ef5f958d8944019593fe2da9008c'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 湖南移动随e加尊享联名会员19.9元
        province: '湖南',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f95a886500195ac0056370054'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // {
    //     // 馨宁-辽宁移动-明星来电黄金plus版20元
    //     province: '辽宁',
    //     city: [],
    //     operator: '移动',
    //     shieldedCities: [],
    //     specialProIds: ['2c98ef5f95ac3c040195ad5fffbe0042'],
    //     // timeRange: {startHour: 7, endHour: 18, endMinute: 59}
    //     timeRange: {startHour: 19, endHour: 6, endMinute: 59}
    // },
    {
        // 智酷-河南电信-30元20G全国通用流量包-融合版
        province: '河南',
        city: [],
        operator: '电信',
        shieldedCities: [],
        specialProIds: ['2c98ef5f95c788990195d55b4c8300f7'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 智酷-河南移动-咪咕足球20元
        province: '河南',
        city: ['鹤壁'],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f95f0814d0195f0b5756b002f'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 秋末-电信权益商城尊享会员
        province: '新疆',
        city: [],
        operator: '电信',
        shieldedCities: ['喀什', '和田', '阿克苏', '克孜勒苏'],
        specialProIds: ['2c98ef5f958d8944019593fe2da9008c'],
        timeRange: null
    },
    {
        // 三发-广东电信-天翼云盘铂金会员20元 产品屏蔽深圳
        province: '广东',
        city: ['深圳'],
        operator: '电信',
        shieldedCities: [],
        specialProIds: ['2c98ef5f96386f2d019638c092b4000c'],
        timeRange: {startHour: 8, endHour: 23, endMinute: 59}
    },
    {
        // 易尊-湖北移动-咪咕短剧情感精品会员15元产品投放时间07:00-22:00
        province: '湖北',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f9642cf70019642e05609000f'],
        timeRange: {startHour: 7, endHour: 22}
    },
    {
        // 湖北移动-铂金会员荆楚流量版20元产品投放时间7-24点
        province: '湖北',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f9642cf70019642f8c3d3002b'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 中网-湖南移动-5G新通话-明星来电一档明星C包
        province: '湖南',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f961ec9fe01961eee1c450009'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 尊享铂金VIP
        province: '',
        city: [],
        operator: '',
        shieldedCities: [],
        specialProIds: ['2c98ef5f95eef6700195ef4568560022'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 悦享铂金VIP
        province: '',
        city: [],
        operator: '',
        shieldedCities: [],
        specialProIds: ['2c98ef5f95eef6700195ef461fb60023'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 三发-新疆电信-视频会员N选一15元权益包
        province: '新疆',
        city: [],
        operator: '电信',
        shieldedCities: ['和田', '阿克苏', '克孜勒苏'],
        specialProIds: ['2c98ef5f9400dba001941555b97c0198'],
        timeRange: {startHour: 8, endHour: 23, endMinute: 59}
    },
    {
        // 湖北移动-明星来电白银组合包B15元产品投放时间8-24点
        province: '湖北',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f94170fed019426451c410156'],
        timeRange: {startHour: 8, endHour: 23, endMinute: 59}
    },
    {
        // 内蒙古移动-移动云盘畅影4K会员19.9元投放时间7-24点，屏蔽赤峰
        province: '内蒙古',
        city: [],
        operator: '移动',
        shieldedCities: ['赤峰'],
        specialProIds: ['2c98ef5f9666445a01966bec0e20021b'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 智酷-新疆电信-18元电信通信助手，屏蔽克孜勒苏
        province: '新疆',
        city: [],
        operator: '电信',
        shieldedCities: ['克孜勒苏'],
        specialProIds: ['2c98ef5f967bd6a701968559c31004f7'],
        timeRange: null
    },
    {
        // 海南联通-所有产品限制投放时间8-23点
        province: '海南',
        city: [],
        operator: '联通',
        shieldedCities: [],
        specialProIds: [],
        timeRange: {startHour: 8, endHour: 23}
    },
    {
        // 秋末-山西电信-优酷会员权益15GB定向流量包投放时间修改
        province: '山西',
        city: [],
        operator: '电信',
        shieldedCities: [],
        specialProIds: ['2c98ef5f96ae5e0e0196b2da7bae0094'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 中网-移动-音乐全曲包乐享音乐20元 产品投放时间限制为7-24点
        province: '',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f96c83b2c0196c89277df000e'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    {
        // 快付-辽宁移动-19.9元快读联名会员-融合版 产品投放时间限制为7-24点
        province: '辽宁',
        city: [],
        operator: '移动',
        shieldedCities: [],
        specialProIds: ['2c98ef5f94170fed01941a57bac2002e'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 千辉-四川移动-咪咕短剧风尚精品会员订阅包-融合版
    {
        province: '',
        city: [],
        shieldedCities: ['雅安', '成都'],
        operator: '',
        specialProIds: ['2c98ef5f95f0814d0195f0abf6060018'],
        timeRange: null
    },
    // 千辉-四川移动-5G极速流量包-30元10GB-融合版
    {
        province: '',
        city: [],
        shieldedCities: ['雅安', '成都'],
        operator: '',
        specialProIds: ['2c98ef5f96d7be990196d890106c0069'],
        timeRange: null
    },
    // 千辉-福建移动-咪咕视频30G流量包-融合版
    {
        province: '',
        city: [],
        shieldedCities: ['雅安', '成都'],
        operator: '',
        specialProIds: ['2c98ef5f95f94d650195fa7c93d4005d'],
        timeRange: null
    },
    // 平治-移动-咪咕音乐-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f967bd6a7019680e9f2c20371'],
        timeRange: {startHour: 8, endHour: 19, endMinute: 59}
    },
    // 内蒙古移动-5G新通话-明星来电黄金组合包C-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f967bd6a70196845c6ffd047e'],
        timeRange: {startHour: 7, endHour: 22, endMinute: 59}
    },
    // 内蒙古移动-健康管家尊享包-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f967bd6a7019684592adc047c'],
        timeRange: {startHour: 7, endHour: 22, endMinute: 59}
    },
    // 内蒙古移动-10GB流量加油站-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f967bd6a70196846371a40481'],
        timeRange: {startHour: 7, endHour: 22, endMinute: 59}
    },
    // 启衡-多省移动-15元联合会员
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '移动',
        specialProIds: ['2c98ef5f96ed10b00196fadc843500e7'],
        timeRange: {startHour: 8, endHour: 23, endMinute: 59}
    },
    // 中网-湖南移动-和教育心理健康30元-融合版
    {
        province: '',
        city: [],
        shieldedCities: ['怀化','邵阳','郴州','吉首','永州'],
        operator: '',
        specialProIds: ['2c98ef5f9715888b019715b7b8150009'],
        timeRange: null
    },
    // 秋末-辽宁联通-30元商户立减金
    {
        province: '',
        city: [],
        shieldedCities: ['抚顺','铁岭','盘锦'],
        operator: '',
        specialProIds: ['2c98ef5f95a886500195a8be4f7b001d'],
        timeRange: null
    },
    // 辽宁联通-联通爱听精品视听酷享版20元/月
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '联通',
        specialProIds: ['2c98ef5f97348944019735c98e410040'],
         timeRange: {startHour: 8, endHour: 23, endMinute: 59}
    },
    //  丰享-重庆移动-5G新通话25元
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '移动',
        specialProIds: ['2c98ef5f9807a8600198080e553b0006'],
         timeRange: {startHour: 8, endHour: 22, endMinute: 59}
    },
    // 广东电信-29.9元10G流量+本地生活(商超)权益包
    {
        province: '广东',
        city: [],
        shieldedCities: [],
        operator: '电信',
        specialProIds: ['2c98ef5f98732838019878fe99af0027'],
        timeRange: {startHour: 6, endHour: 23, endMinute: 59}
    },
    // 迅雷-25元异型流量权益红包-优化融合
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f982c6f3e01983631bd0909a3'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 迅雷-爱奇艺10GB流量加会员包-优化-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f97d4da110197e7d0e02000ce'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 致为-广东移动-20元咪咕足球通月包-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f98c0526b0198c1235d7201bf'],
        timeRange: {startHour: 7, endHour: 22, endMinute: 59}
    },
    // 中网-广西移动-5G新通话-明星来电一档明星D包-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f981658290198178f0c44004c'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 中网-广西移动-5G新通话-明星来电一档明星C包-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f9815fdb40198160e27c40002'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 新疆移动-5G新通话-明星来电一档明星D包-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f98129f3e019812bcf5830007'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 新疆移动-5G新通话-明星来电一档明星C包-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f98129f3e019812b9ef350005'],
        timeRange: {startHour: 7, endHour: 23, endMinute: 59}
    },
    // 迅雷-爱奇艺10GB流量加会员包-优化-融合版
    {
        province: '',
        city: [],
        shieldedCities: [],
        operator: '',
        specialProIds: ['2c98ef5f97d4da110197e7d0e02000ce'],
        timeRange: {startHour: 7, startMinute: 30, endHour: 23, endMinute: 59}
    },
];

// 检查时间是否在特殊时间段内
function checkSpecialTime(date, timeRange) {
    if (!timeRange) return false; // 如果没有时间限制，则不需要关闭

    const hours = date.getHours();
    const minutes = date.getMinutes();

    // 时间范围不跨天
    if ((hours > timeRange.startHour ||
            (hours === timeRange.startHour && minutes >= (timeRange.startMinute || 0))) &&
        (hours < timeRange.endHour ||
            (hours === timeRange.endHour && minutes <= (timeRange.endMinute || 0)))) {
        return false; // 当前时间在允许时间范围内，不需要关闭
    }

    return true; // 当前时间在允许时间范围外，需要关闭
}


// 检查是否需要关闭特定产品
function checkAndCloseSpecialProduct(infoObj, autoLinkInfo, packageName) {
    const now = new Date();

    // 找到所有匹配的基础配置
    const matchingConfigs = specialConfigs.filter(config => {
        const isSpecialProject = config.specialProIds.length === 0 || config.specialProIds.includes(infoObj.fusionProId);
        const isTargetProvinceAndOperator = (config.province === '' || config.province === autoLinkInfo.province) &&
            (config.operator === '' || config.operator === autoLinkInfo.cmcc);
        const isShieldedPackageName = !config.shieldedPackageName || config.shieldedPackageName.includes(packageName);

        return isSpecialProject && isTargetProvinceAndOperator && isShieldedPackageName;
    });

    // 如果没有匹配的配置，则允许投放
    if (matchingConfigs.length === 0) {
        return false;
    }

    // 优先检查省份特定配置
    const provinceConfig = matchingConfigs.find(config => config.province === autoLinkInfo.province);

    if (provinceConfig) {
        // 检查城市限制
        if (provinceConfig.shieldedCities && provinceConfig.shieldedCities.includes(autoLinkInfo.city)) {
            console.log('当前城市在省份特定配置的屏蔽列表中');
            return true;
        }

        // 检查时间限制
        const shouldClose = checkSpecialTime(now, provinceConfig.timeRange);
        console.log('shouldClose', shouldClose)
        if (shouldClose) {
            console.log('当前时间不在省份特定投放时间范围内');
            return true;
        }
    } else {
        // 如果没有省份特定配置，使用全国配置
        const globalConfig = matchingConfigs.find(config => config.province === '');
        if (globalConfig) {
            // 检查全国配置的时间限制
            const shouldClose = checkSpecialTime(now, globalConfig.timeRange);
            console.log('shouldClose', shouldClose)
            if (shouldClose) {
                console.log('当前时间不在全国配置的投放时间范围内');
                return true;
            }
        }
    }

    return false;
}
