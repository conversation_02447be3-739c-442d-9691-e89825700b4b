#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 257949696 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3771), pid=19224, tid=20752
#
# JRE version:  (17.0.7+10) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.7+10-b829.16, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://codeup.aliyun.com': 

Host: AMD Ryzen 7 4800U with Radeon Graphics         , 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3235)
Time: Mon Apr  1 14:34:22 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.3235) elapsed time: 0.021099 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000180e007c880):  JavaThread "Unknown thread" [_thread_in_vm, id=20752, stack(0x0000007d51700000,0x0000007d51800000)]

Stack: [0x0000007d51700000,0x0000007d51800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x683cca]
V  [jvm.dll+0x8424b4]
V  [jvm.dll+0x843cae]
V  [jvm.dll+0x844313]
V  [jvm.dll+0x24ad2f]
V  [jvm.dll+0x680b99]
V  [jvm.dll+0x67526a]
V  [jvm.dll+0x30b3cb]
V  [jvm.dll+0x312876]
V  [jvm.dll+0x36221e]
V  [jvm.dll+0x36244f]
V  [jvm.dll+0x2e14a8]
V  [jvm.dll+0x2e2414]
V  [jvm.dll+0x814441]
V  [jvm.dll+0x36ffe1]
V  [jvm.dll+0x7f3a1c]
V  [jvm.dll+0x3f305f]
V  [jvm.dll+0x3f4b91]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff9d89fb098, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x00000180e00e7ac0 GCTaskThread "GC Thread#0" [stack: 0x0000007d51800000,0x0000007d51900000] [id=23568]
  0x00000180e00f8770 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000007d51900000,0x0000007d51a00000] [id=19420]
  0x00000180e00f9180 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000007d51a00000,0x0000007d51b00000] [id=31220]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9d81b2087]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000180e0077240] Heap_lock - owner thread: 0x00000180e007c880

Heap address: 0x000000070a200000, size: 3934 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x000000070a200000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9d859df59]

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.013 Loaded shared library D:\JetBrains\WebStorm 2023.1.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff65dd30000 - 0x00007ff65dd3a000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\java.exe
0x00007ffa1f110000 - 0x00007ffa1f326000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa1d3b0000 - 0x00007ffa1d474000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa1c8f0000 - 0x00007ffa1cc96000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa1c620000 - 0x00007ffa1c731000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff9ff120000 - 0x00007ff9ff137000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\jli.dll
0x00007ffa04b80000 - 0x00007ffa04b9b000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ffa1ce60000 - 0x00007ffa1d00d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa1c480000 - 0x00007ffa1c4a6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa1d0a0000 - 0x00007ffa1d0c9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa1cca0000 - 0x00007ffa1cdb8000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa1cdc0000 - 0x00007ffa1ce5a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff9fe140000 - 0x00007ff9fe3d3000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98\COMCTL32.dll
0x00007ffa1e340000 - 0x00007ffa1e3e7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa1d170000 - 0x00007ffa1d1a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa15090000 - 0x00007ffa1509c000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff9e0980000 - 0x00007ff9e0a0d000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\msvcp140.dll
0x00007ff9d7ec0000 - 0x00007ff9d8b2e000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\server\jvm.dll
0x00007ffa1ec80000 - 0x00007ffa1ed32000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa1ea10000 - 0x00007ffa1eab8000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa1c450000 - 0x00007ffa1c478000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffa1efb0000 - 0x00007ffa1f0c5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa14ea0000 - 0x00007ffa14ed4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa16400000 - 0x00007ffa16409000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa132c0000 - 0x00007ffa132ca000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa1eee0000 - 0x00007ffa1ef51000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa1b490000 - 0x00007ffa1b4a8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa15080000 - 0x00007ffa1508a000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\jimage.dll
0x00007ffa19b30000 - 0x00007ffa19d63000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa1e680000 - 0x00007ffa1ea08000 	C:\WINDOWS\System32\combase.dll
0x00007ffa1e3f0000 - 0x00007ffa1e4c7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9f9400000 - 0x00007ff9f9432000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa1c7b0000 - 0x00007ffa1c82a000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff9f69b0000 - 0x00007ff9f69d5000 	D:\JetBrains\WebStorm 2023.1.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\JetBrains\WebStorm 2023.1.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98;D:\JetBrains\WebStorm 2023.1.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://codeup.aliyun.com': 
java_class_path (initial): D:/JetBrains/WebStorm 2023.1.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/JetBrains/WebStorm 2023.1.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4125097984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4125097984                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:/Git/mingw64/libexec/git-core;D:/Git/mingw64/libexec/git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Git\cmd;C:\Program Files (x86)\HIK-share\;D:\Soft\;D:\Soft\xftp\;D:\Tencent\΢��web�����߹���\dll;C:\Program Files\TortoiseGit\bin;C:\Program Files (x86)\Yarn\bin\;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\nodejs;C:\Users\<USER>\AppData\Local\Yarn\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=xushe
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:

[error occurred during error reporting (JNI global references), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9d7f77fc0]


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3235)
OS uptime: 0 days 7:40 hours

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 15734M (1109M free)
TotalPageFile size 28534M (AvailPageFile size 139M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 70M, peak: 316M

vm_info: OpenJDK 64-Bit Server VM (17.0.7+10-b829.16) for windows-amd64 JRE (17.0.7+10-b829.16), built on 2023-06-02 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
